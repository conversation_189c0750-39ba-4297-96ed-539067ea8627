﻿<%@ Page Title="版本对比 | 免费版专业版功能对比 | 选择适合版本" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" EnableViewState="false" %>
<%@ Import Namespace="CommonLib" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
<meta name="description" content="OCR文字识别助手版本对比：免费版永久使用，专业版批量处理，旗舰版企业定制。选择最适合的版本，满足个人到企业全场景需求！">
<meta name="keywords" content="OCR版本对比,文字识别版本,OCR会员,专业版OCR,免费版OCR,OCR升级,批量识别版本">

<meta property="og:title" content="OCR助手版本对比 | 选择最适合的版本" />
<meta property="og:description" content="OCR助手版本对比：免费版永久使用，专业版批量处理，旗舰版企业定制。AI智能推荐最适合版本，满足个人到企业全场景需求。" />
<meta property="og:type" content="website" />
<meta property="og:url" content="<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/version.aspx") %>" />

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "OCR文字识别助手",
    "description": "专业OCR文字识别软件，提供免费版、专业版、旗舰版多种选择，满足不同用户需求。",
    "image": "https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7131.**********-2023-06-05-*************.gif",
    "brand": {
        "@type": "Brand",
        "name": "OCR文字识别助手"
    },
    "manufacturer": {
        "@type": "Organization",
        "name": "OCR文字识别助手团队",
        "url": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/") %>"
    },
    "seller": {
        "@type": "Organization",
        "name": "OCR文字识别助手",
        "url": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/") %>"
    },
    "category": "软件应用",
    "sku": "OCR-ASSISTANT-2024",
    "gtin": "OCR-ASSISTANT-GTIN-2024",
    "warranty": {
        "@type": "WarrantyPromise",
        "durationOfWarranty": "P1Y",
        "warrantyScope": "软件功能保障"
    },
    "offers": [
        {
            "@type": "Offer",
            "name": "免费版",
            "description": "基础文字识别功能，永久免费使用",
            "price": "0",
            "priceCurrency": "CNY",
            "availability": "https://schema.org/InStock",
            "priceValidUntil": "2030-12-31",
            "seller": {
                "@type": "Organization",
                "name": "OCR文字识别助手"
            },
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "applicableCountry": "CN",
                "returnPolicySeasonalOverride": false,
                "merchantReturnDays": 7,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/FreeReturn"
            },
            "shippingDetails": {
                "@type": "OfferShippingDetails",
                "shippingDestination": {
                    "@type": "DefinedRegion",
                    "addressCountry": "CN"
                },
                "shippingRate": {
                    "@type": "MonetaryAmount",
                    "value": "0",
                    "currency": "CNY"
                },
                "deliveryTime": {
                    "@type": "ShippingDeliveryTime",
                    "handlingTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    },
                    "transitTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    }
                }
            },
            "category": "免费版本"
        },
        {
            "@type": "Offer",
            "name": "个人版",
            "description": "基础功能，适合个人用户日常使用",
            "price": "36",
            "priceCurrency": "CNY",
            "availability": "https://schema.org/InStock",
            "priceValidUntil": "2030-12-31",
            "seller": {
                "@type": "Organization",
                "name": "OCR文字识别助手"
            },
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "applicableCountry": "CN",
                "returnPolicySeasonalOverride": false,
                "merchantReturnDays": 7,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/FreeReturn"
            },
            "shippingDetails": {
                "@type": "OfferShippingDetails",
                "shippingDestination": {
                    "@type": "DefinedRegion",
                    "addressCountry": "CN"
                },
                "shippingRate": {
                    "@type": "MonetaryAmount",
                    "value": "0",
                    "currency": "CNY"
                },
                "deliveryTime": {
                    "@type": "ShippingDeliveryTime",
                    "handlingTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    },
                    "transitTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    }
                }
            },
            "category": "个人版本"
        },
        {
            "@type": "Offer",
            "name": "专业版",
            "description": "表格识别、公式识别等高级功能",
            "price": "58",
            "priceCurrency": "CNY",
            "availability": "https://schema.org/InStock",
            "priceValidUntil": "2030-12-31",
            "seller": {
                "@type": "Organization",
                "name": "OCR文字识别助手"
            },
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "applicableCountry": "CN",
                "returnPolicySeasonalOverride": false,
                "merchantReturnDays": 7,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/FreeReturn"
            },
            "shippingDetails": {
                "@type": "OfferShippingDetails",
                "shippingDestination": {
                    "@type": "DefinedRegion",
                    "addressCountry": "CN"
                },
                "shippingRate": {
                    "@type": "MonetaryAmount",
                    "value": "0",
                    "currency": "CNY"
                },
                "deliveryTime": {
                    "@type": "ShippingDeliveryTime",
                    "handlingTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    },
                    "transitTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    }
                }
            },
            "category": "专业版本"
        },
        {
            "@type": "Offer",
            "name": "旗舰版",
            "description": "企业级功能、API接口、定制服务",
            "price": "158",
            "priceCurrency": "CNY",
            "availability": "https://schema.org/InStock",
            "priceValidUntil": "2030-12-31",
            "seller": {
                "@type": "Organization",
                "name": "OCR文字识别助手"
            },
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "applicableCountry": "CN",
                "returnPolicySeasonalOverride": false,
                "merchantReturnDays": 7,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/FreeReturn"
            },
            "shippingDetails": {
                "@type": "OfferShippingDetails",
                "shippingDestination": {
                    "@type": "DefinedRegion",
                    "addressCountry": "CN"
                },
                "shippingRate": {
                    "@type": "MonetaryAmount",
                    "value": "0",
                    "currency": "CNY"
                },
                "deliveryTime": {
                    "@type": "ShippingDeliveryTime",
                    "handlingTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    },
                    "transitTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    }
                }
            },
            "category": "旗舰版本"
        }
    ],
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "bestRating": "5",
        "worstRating": "1",
        "ratingCount": "<%=((System.DateTime.Now.ToUniversalTime().Ticks - 6213559***********) / *************3).ToString() %>"
    }
}
</script>
</asp:Content>

<asp:Content ID="BreadcrumbContent" ContentPlaceHolderID="BreadcrumbSchema" runat="server">
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "首页",
                "item": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/default.aspx") %>"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "版本对比",
                "item": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/version.aspx") %>"
            }
        ]
    }
    </script>
<style>
.version-page{background:#f8f9fa;min-height:100vh;padding:65px 0}.container{max-width:1200px;margin:0 auto;padding:0 20px}.hero-section{text-align:center;margin:10px 0 40px}.hero-title{font-size:2.5rem;font-weight:700;color:#333;margin-bottom:16px;letter-spacing:-0.5px}.hero-subtitle{font-size:1.2rem;color:#606879;margin-bottom:40px;line-height:1.5}.version-cards{display:grid;grid-template-columns:repeat(4,1fr);gap:24px;margin-bottom:80px}.version-card{background:#fff;border-radius:16px;padding:28px 20px;text-align:center;box-shadow:0 4px 20px rgba(0,0,0,0.06);border:2px solid transparent;transition:all 0.3s ease;position:relative}.version-card:hover{transform:translateY(-4px);box-shadow:0 12px 32px rgba(0,0,0,0.12)}.version-card.popular{border-color:#007cfa}.version-card.popular::before{content:'🔥 推荐';position:absolute;top:-10px;left:50%;transform:translateX(-50%);background:#007cfa;color:#fff;padding:4px 12px;border-radius:12px;font-size:11px;font-weight:600}.version-card.ai-recommended-card{border-color:#28a745 !important;background:linear-gradient(135deg,rgba(40,167,69,0.08) 0%,rgba(40,167,69,0.12) 100%) !important;transform:translateY(-6px) !important;box-shadow:0 16px 40px rgba(40,167,69,0.25) !important;animation:aiRecommendPulse 2s ease-in-out}.version-card.ai-recommended-card::before{content:'🤖 AI智能推荐' !important;position:absolute;top:-10px;left:50%;transform:translateX(-50%);background:#28a745 !important;color:#fff !important;padding:4px 12px;border-radius:12px;font-size:11px;font-weight:600}.card-icon{font-size:2.5rem;margin-bottom:12px;display:block}.card-title{font-size:1.3rem;font-weight:700;color:#333;margin-bottom:6px}.card-subtitle{color:#606879;font-size:0.9rem;margin-bottom:20px;line-height:1.4}.card-core-features{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:16px;justify-content:center}.core-feature{display:flex;align-items:center;background:#f8f9fa;padding:6px 12px;border-radius:20px;font-size:13px;color:#555;border:1px solid #e9ecef}.core-feature .feature-icon{margin-right:6px;font-size:14px}.feature-summary{text-align:center;margin-bottom:20px;padding:12px;background:#f8f9fa;border-radius:8px}.feature-count{display:block;font-size:13px;color:#007cfa;font-weight:600;margin-bottom:4px}.device-count{font-size:12px;color:#666}.smart-recommendation{background:linear-gradient(135deg,rgba(102,126,234,0.12) 0%,rgba(118,75,162,0.12) 100%);border-radius:16px;padding:22px 20px;margin-bottom:40px;text-align:center;border:2px solid rgba(102,126,234,0.3);position:relative;overflow:hidden;transition:all 0.3s ease;box-shadow:0 8px 24px rgba(102,126,234,0.18)}.smart-recommendation::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.4),transparent);animation:shimmer 3s infinite}.smart-recommendation:hover{transform:translateY(-4px);box-shadow:0 12px 35px rgba(102,126,234,0.28);border-color:rgba(102,126,234,0.45)}.recommendation-trigger{display:flex;align-items:center;justify-content:center;gap:12px;flex-wrap:wrap}.recommendation-trigger .fa-robot{animation:robotPulse 2s ease-in-out infinite}.quiz-btn{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:white;border:none;padding:12px 24px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 12px rgba(102,126,234,0.35)}.quiz-btn:hover{transform:translateY(-2px);box-shadow:0 6px 16px rgba(102,126,234,0.45)}.card-button{display:block;width:100%;padding:12px 20px;border-radius:10px;font-size:15px;font-weight:600;text-decoration:none;transition:all 0.3s ease;border:none;cursor:pointer}.btn-v0{background:rgb(23,198,83);color:#fff}.btn-v0:hover{background:rgb(18,158,66);color:#fff;text-decoration:none}.btn-v-1{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);color:#fff}.btn-v-1:hover{background:linear-gradient(89.95deg,#5555EE 11.5%,#2AAFEE 100.01%);color:#fff;text-decoration:none}.btn-v1{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);color:#F9D9A8}.btn-v1:hover{background:linear-gradient(to right,#3A3A3A 5.77%,#0F0A05 100%);color:#F9D9A8;text-decoration:none}.btn-v3{background:linear-gradient(to right,#FFEBC1 21.65%,#FFE5B7 79.13%);color:#944800}.btn-v3:hover{background:linear-gradient(to right,#FFE0A8 21.65%,#FFDAA4 79.13%);color:#944800;text-decoration:none}.highlights-section{background:#fff;border-radius:20px;padding:60px 40px;margin-bottom:60px;text-align:center}.highlights-title{font-size:2rem;font-weight:700;color:#333;margin:0 0 40px 0}.highlights-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:40px}.highlight-item{text-align:center}.highlight-icon{font-size:3rem;margin-bottom:16px;display:block}.highlight-title{font-size:1.2rem;font-weight:600;color:#333;margin-bottom:12px}.highlight-desc{color:#606879;line-height:1.5}.scenarios-section{margin-bottom:60px}.scenarios-title{font-size:2rem;font-weight:700;color:#333;text-align:center;margin:0 0 40px 0}.scenarios-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:32px}.scenario-card{background:#fff;border-radius:16px;padding:32px;box-shadow:0 8px 32px rgba(0,0,0,0.08);transition:all 0.3s ease}.scenario-card:hover{transform:translateY(-4px);box-shadow:0 12px 40px rgba(0,0,0,0.12)}.scenario-icon{font-size:2.5rem;margin-bottom:16px}.scenario-title{font-size:1.3rem;font-weight:600;color:#333;margin-bottom:12px}.scenario-desc{color:#606879;line-height:1.6;margin-bottom:16px}.scenario-features{list-style:none;padding:0;margin:0}.scenario-features li{color:#555;font-size:14px;padding:4px 0;display:flex;align-items:center}.scenario-features li::before{content:'•';color:#007cfa;margin-right:8px;font-weight:bold}.faq-section{background:#fff;border-radius:20px;padding:60px 40px;margin-bottom:60px}.faq-title{font-size:2rem;font-weight:700;color:#333;text-align:center;margin:0 0 40px 0}.faq-item{margin-bottom:24px;border-bottom:1px solid #f0f0f0;padding-bottom:24px}.faq-item:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0}.faq-question{font-size:1.1rem;font-weight:600;color:#333;margin-bottom:12px}.faq-answer{color:#606879;line-height:1.6}
@keyframes shimmer{0%{left:-100%}100%{left:100%}}
@keyframes robotPulse{0%,100%{transform:scale(1);color:#007cfa}50%{transform:scale(1.1);color:#667eea}}
@keyframes sparkle{0%,100%{transform:scale(1) rotate(0deg);opacity:1}25%{transform:scale(1.1) rotate(-5deg);opacity:0.8}75%{transform:scale(1.05) rotate(5deg);opacity:0.9}}
@keyframes aiRecommendPulse{0%,100%{box-shadow:0 16px 40px rgba(40,167,69,0.25)}50%{box-shadow:0 20px 50px rgba(40,167,69,0.35)}}
@media (max-width:1200px){.version-cards{grid-template-columns:repeat(2,1fr);gap:20px}}
@media (max-width:768px){.hero-title{font-size:2rem}.version-cards{grid-template-columns:1fr;gap:20px}.version-card{padding:24px 16px}.card-icon{font-size:2rem;margin-bottom:10px}.card-title{font-size:1.2rem}.card-subtitle{font-size:0.85rem;margin-bottom:16px}.card-core-features{gap:6px}.core-feature{padding:4px 8px;font-size:12px}.core-feature .feature-icon{font-size:13px}.feature-summary{padding:10px;margin-bottom:16px}.feature-count{font-size:12px}.device-count{font-size:11px}.card-button{padding:10px 16px;font-size:14px}.highlights-grid{grid-template-columns:1fr;gap:32px}.scenarios-grid{grid-template-columns:1fr;gap:24px}.highlights-section{padding:40px 24px}.faq-section{padding:40px 24px}}
</style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
<div class="version-page">
<div class="container">
<section class="hero-section">
<h1 class="hero-title"><b style="color: #007cfa;">OCR文字识别助手</b> - 让每一个文字都触手可及</h1>
<p class="hero-subtitle">选择专属版本，开启高效办公新体验！</p>
</section>
<div class="smart-recommendation">
<div class="recommendation-trigger">
<i class="fa fa-robot" style="color: #007cfa; margin-right: 8px; font-size: 16px;"></i>
<span style="color: #666; margin-right: 12px;">不知道选哪个？</span>
<button type="button" class="quiz-btn" onclick="openSmartRecommendationIframe(); return false;">
<i class="fa fa-magic" style="margin-right: 6px; animation: sparkle 1.5s ease-in-out infinite;"></i>让AI帮我选择合适的版本
</button>
</div>
</div>
<section class="version-cards">
<%var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();
lstUserTypes.Insert(0, UserTypeHelper.GetUserType(UserTypeEnum.体验版));
var cardIcons = new string[] { "🌱", "👨‍💼", "🏆", "👑" };
var cardDescriptions = new string[] { "基础功能，永久免费", "个人专属，轻松识别", "专业高效，精确稳定", "旗舰体验，最佳选择" };
for (int i = 0; i < lstUserTypes.Count; i++){
var item = lstUserTypes[i];
var url = item.Type.GetHashCode() == 0 ? Account.Web.CommonRequest.GetDownLoadUrl(Request) : "Upgrade.aspx?type=" + item.Type.GetHashCode();
var isPopular = i == 2;%>
<div class="version-card<%=isPopular ? " popular" : "" %>" data-type-hash="<%=item.Type.GetHashCode() %>">
<span class="card-icon"><%=cardIcons[i] %></span>
<h3 class="card-title"><%=item.Type.ToString().Replace("体验","免费") %></h3>
<p class="card-subtitle"><%=cardDescriptions[i] %></p>
<div class="card-core-features">
<div class="core-feature"><span class="feature-icon">📸</span><span>截图识别</span></div>
<%if (item.IsSupportLocalOcr) { %><div class="core-feature"><span class="feature-icon">🔒</span><span>离线识别</span></div><%} %>
<%if (item.IsSupportTable) { %><div class="core-feature"><span class="feature-icon">📋</span><span>表格识别</span></div><%} %>
<%if (item.IsSupportMath) { %><div class="core-feature"><span class="feature-icon">📊</span><span>公式识别</span></div><%} %>
<%if (item.IsSupportBatch) { %><div class="core-feature"><span class="feature-icon">🔢</span><span>批量识别</span></div><%} %>
</div>
<div class="feature-summary">
<%int supportedCount = 1 + (item.IsSupportLocalOcr?1:0) + (item.IsSupportImageFile?1:0) + (item.IsSupportTable?1:0) + (item.IsSupportMath?1:0) + (item.IsSupportDocFile?1:0) + (item.IsSupportBatch?1:0) + (item.IsSupportTranslate?1:0);%>
<span class="feature-count">包含 <%=supportedCount %> 项核心功能</span>
<%if (item.MaxLoginCount > 1) { %><span class="device-count">支持 <%=item.MaxLoginCount %> 设备同时使用</span><%} %>
</div>
<a href="<%=url %>" class="card-button btn-v<%=item.Type.GetHashCode() %>"><%=item.Type.GetHashCode() == 0 ? "免费下载" : "升级到" + item.Type.ToString() %></a>
</div>
<%}%>
</section>
<section class="scenarios-section">
<h2 class="scenarios-title">适用场景</h2>
<div class="scenarios-grid">
<div class="scenario-card">
<div class="scenario-icon">🎓</div>
<h3 class="scenario-title">学术研究 · 高效学习</h3>
<p class="scenario-desc">论文数字化，笔记电子化，知识系统化</p>
<ul class="scenario-features">
<li><strong>论文扫描</strong> - PDF学术文献快速提取</li>
<li><strong>公式识别</strong> - 数学物理公式精准转换</li>
<li><strong>手写笔记</strong> - 课堂记录瞬间数字化</li>
<li><strong>多语言文献</strong> - 外文资料一键翻译</li>
</ul>
</div>
<div class="scenario-card">
<div class="scenario-icon">💼</div>
<h3 class="scenario-title">商务办公 · 专业高效</h3>
<p class="scenario-desc">文档自动化，数据智能化，工作专业化</p>
<ul class="scenario-features">
<li><strong>合同扫描</strong> - 纸质合同快速电子化</li>
<li><strong>表格提取</strong> - 复杂表格结构完整保留</li>
<li><strong>批量处理</strong> - 大量文档一次性识别</li>
<li><strong>多设备协作</strong> - 团队成员同步使用</li>
</ul>
</div>
<div class="scenario-card">
<div class="scenario-icon">🏢</div>
<h3 class="scenario-title">企业应用 · 规模化部署</h3>
<p class="scenario-desc">满足企业级需求，提升组织整体效率</p>
<ul class="scenario-features">
<li><strong>档案数字化</strong> - 历史文档批量转换</li>
<li><strong>发票识别</strong> - 财务报销自动化处理</li>
<li><strong>证件扫描</strong> - 身份信息快速录入</li>
<li><strong>离线部署</strong> - 内网环境安全可控</li>
</ul>
</div>
</div>
</section>
<section class="highlights-section">
<h2 class="highlights-title">为什么选择我们</h2>
<div class="highlights-grid">
<div class="highlight-item">
<span class="highlight-icon">🚀</span>
<h3 class="highlight-title">AI驱动，精准无误</h3>
<p class="highlight-desc">深度学习算法持续优化，清晰文档识别准确率99.5%+，复杂场景依然稳定可靠，让每一次识别都值得信赖</p>
</div>
<div class="highlight-item">
<span class="highlight-icon">⚡</span>
<h3 class="highlight-title">毫秒响应，极速体验</h3>
<p class="highlight-desc">截图即识别，无需等待。优化算法架构，平均响应时间<200ms，让效率成为习惯</p>
</div>
<div class="highlight-item">
<span class="highlight-icon">🌐</span>
<h3 class="highlight-title">全能识别，一站解决</h3>
<p class="highlight-desc">支持文字、公式、表格、文档、多语言翻译等全场景识别，一个工具替代多个软件，简化您的工作流</p>
</div>
<div class="highlight-item">
<span class="highlight-icon">💎</span>
<h3 class="highlight-title">持续进化，永不过时</h3>
<p class="highlight-desc">算法持续更新，功能不断增强，一次购买长期受益。我们的成长就是您效率的提升</p>
</div>
</div>
</section>

<section class="faq-section">
<h2 class="faq-title">常见问题</h2>
<div class="faq-item">
<h3 class="faq-question">🤔 如何选择适合的版本？</h3>
<p class="faq-answer"><strong>免费版</strong>适合偶尔使用的用户，支持基础截图识别；<strong>个人版</strong>适合日常办公，增加图片识别和翻译功能；<strong>专业版</strong>适合专业用户，支持离线识别、公式表格识别等高级功能；<strong>旗舰版</strong>功能最全面，支持批量处理和多设备同步。建议根据使用频率和功能需求选择。</p>
</div>
<div class="faq-item">
<h3 class="faq-question">🔒 哪些版本支持离线使用？</h3>
<p class="faq-answer">个人版、专业版和旗舰版均支持离线识别功能。离线模式下，您的文档和图片在本地处理，数据不会上传到云端，完全保护您的隐私和商业机密安全。</p>
</div>
<div class="faq-item">
<h3 class="faq-question">📊 识别准确率和速度如何？</h3>
<p class="faq-answer">采用深度学习AI算法，对清晰印刷文档识别准确率可达<strong>99.5%以上</strong>，手写文字准确率约85-95%。平均识别速度毫秒级，支持实时截图识别，大幅提升工作效率。</p>
</div>
<div class="faq-item">
<h3 class="faq-question">📱 可以在多少台设备上使用？</h3>
<p class="faq-answer">免费版仅限单设备使用；个人版支持<strong>2台设备</strong>同时登录；专业版支持<strong>3台设备</strong>；旗舰版支持<strong>5台设备</strong>同时使用，满足多设备办公需求。</p>
</div>
<div class="faq-item">
<h3 class="faq-question">⚡ 每日使用有限制吗？</h3>
<p class="faq-answer">免费版每日限制使用次数；付费版本大幅提升使用限额，专业版和旗舰版基本可满足重度使用需求。具体限额请参考版本对比表格。</p>
</div>
<div class="faq-item">
<h3 class="faq-question">🔧 支持哪些专业功能？</h3>
<p class="faq-answer">专业版及以上支持<strong>数学公式识别</strong>、<strong>表格识别</strong>等高级功能。旗舰版还支持自选识别通道、批量处理等专业特性。</p>
</div>
<div class="faq-item">
<h3 class="faq-question">💳 如何升级会员？</h3>
<p class="faq-answer">点击上方版本卡片中的"升级"按钮，选择合适的套餐和支付方式即可。支持支付宝、微信支付等多种支付方式，购买后立即生效，享受会员特权。</p>
</div>
<div class="faq-item">
<h3 class="faq-question">🛡️ 数据安全如何保障？</h3>
<p class="faq-answer">我们高度重视用户隐私安全。支持完全离线识别，敏感数据不上云；在线模式采用加密传输，数据不存储；企业用户可选择内网部署，确保数据安全可控。</p>
</div>
</section>
</div>
</div>
<script>
function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}
function getOrigin(){return window.location.protocol+'//'+window.location.host;}
function openSmartRecommendationIframe() {
            var iframeHtml = '<div id="smartRecommendationModal" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);z-index:1000;display:flex;align-items:center;justify-content:center;backdrop-filter:blur(3px);"><div style="background:transparent;border-radius:20px;padding:0;max-width:600px;width:90%;display:flex;flex-direction:column;box-shadow:0 20px 60px rgba(0,0,0,0.3);position:relative;overflow:hidden;"><iframe src="SmartRecommendation.aspx" style="width:100%;height:90vh;border:none;border-radius:20px;background:transparent;" frameborder="0"></iframe></div></div>';
            document.body.insertAdjacentHTML('beforeend', iframeHtml);
            document.body.style.overflow = 'hidden';
            window.addEventListener('message', handleRecommendationMessage);
        }
        
function closeSmartRecommendationIframe() {
            var modal = document.getElementById('smartRecommendationModal');
            if (modal) {
                safeRemove(modal);
                document.body.style.overflow = 'auto';
                window.removeEventListener('message', handleRecommendationMessage);
            }
        }

function handleRecommendationMessage(event){
            if(event.origin!==getOrigin())return;
            var data=event.data;
            switch(data.action){
                case'closeRecommendation':
                    closeSmartRecommendationIframe();
                    break;
                case'handleRecommendation':
                    closeSmartRecommendationIframe();
                    var pendingRecommendation=data.pendingRecommendation;
                    if(pendingRecommendation){
                        if(!pendingRecommendation.userHasSelected){
                            switchToVersion(pendingRecommendation.version);
                        }else if(pendingRecommendation.isApplyRecommendation){
                            switchToVersion(pendingRecommendation.version);
                        }else if(pendingRecommendation.selectedVersion){
                            switchToVersion(pendingRecommendation.selectedVersion);
                        }
                    }
                    break;
            }
        }

function switchToVersion(versionType){
            var typeHash=versionType.substring(1);
            var targetCard=document.querySelector('.version-card[data-type-hash="'+typeHash+'"]');
            if(targetCard){
                highlightRecommendedCard(targetCard);
            }
        }

function highlightRecommendedCard(card){
            var allCards=document.querySelectorAll('.version-card');
            allCards.forEach(function(c){
                c.classList.remove('ai-recommended','ai-recommended-card','popular');
            });
            card.classList.add('ai-recommended-card');
        }

document.addEventListener('click',function(event){
            var modal=document.getElementById('smartRecommendationModal');
            if(modal&&event.target===modal){
                closeSmartRecommendationIframe();
            }
        });

document.addEventListener('keydown',function(event){
            if(event.keyCode===27){
                closeSmartRecommendationIframe();
            }
        });
    </script>
</asp:Content>
