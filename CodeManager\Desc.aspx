﻿<%@ Page Language="C#" AutoEventWireup="true" EnableViewState="false" %>
<%@ Import Namespace="CommonLib" %>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0, shrink-to-fit=no">
<title>VIP会员权益详情 | OCR文字识别助手 - 专业版与旗舰版功能对比</title>
<meta name="description" content="OCR文字识别助手VIP会员权益详细介绍：个人版、专业版、旗舰版功能对比，批量处理、离线识别、API接口等高级功能，选择最适合的会员等级。" />
<meta name="keywords" content="VIP会员,会员权益,专业版,旗舰版,功能对比,价格方案,OCR会员,批量处理,离线识别" />

<meta property="og:title" content="VIP会员权益详情 | OCR文字识别助手" />
<meta property="og:description" content="详细的VIP会员权益介绍，个人版、专业版、旗舰版功能对比，选择最适合您的会员等级。" />
<meta property="og:type" content="website" />
<meta property="og:url" content="<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/desc.aspx") %>" />

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "OCR文字识别助手VIP会员",
    "description": "OCR文字识别助手VIP会员服务，提供专业版和旗舰版多种选择，包含批量处理、离线识别、API接口等高级功能。",
    "image": "https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7131.**********-2023-06-05-*************.gif",
    "brand": {
        "@type": "Brand",
        "name": "OCR文字识别助手"
    },
    "offers": [
        {
            "@type": "Offer",
            "name": "个人版",
            "description": "基础功能，适合个人用户日常使用",
            "price": "36",
            "priceCurrency": "CNY",
            "availability": "https://schema.org/InStock",
            "priceValidUntil": "2030-12-31",
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "applicableCountry": "CN",
                "returnPolicySeasonalOverride": false,
                "merchantReturnDays": 7,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/FreeReturn"
            },
            "shippingDetails": {
                "@type": "OfferShippingDetails",
                "shippingDestination": {
                    "@type": "DefinedRegion",
                    "addressCountry": "CN"
                },
                "shippingRate": {
                    "@type": "MonetaryAmount",
                    "value": "0",
                    "currency": "CNY"
                },
                "deliveryTime": {
                    "@type": "ShippingDeliveryTime",
                    "handlingTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    },
                    "transitTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    }
                }
            }
        },
        {
            "@type": "Offer",
            "name": "专业版",
            "description": "表格识别、公式识别等专业功能",
            "price": "58",
            "priceCurrency": "CNY",
            "availability": "https://schema.org/InStock",
            "priceValidUntil": "2030-12-31",
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "applicableCountry": "CN",
                "returnPolicySeasonalOverride": false,
                "merchantReturnDays": 7,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/FreeReturn"
            },
            "shippingDetails": {
                "@type": "OfferShippingDetails",
                "shippingDestination": {
                    "@type": "DefinedRegion",
                    "addressCountry": "CN"
                },
                "shippingRate": {
                    "@type": "MonetaryAmount",
                    "value": "0",
                    "currency": "CNY"
                },
                "deliveryTime": {
                    "@type": "ShippingDeliveryTime",
                    "handlingTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    },
                    "transitTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    }
                }
            }
        },
        {
            "@type": "Offer",
            "name": "旗舰版",
            "description": "企业级功能、API接口、定制服务",
            "price": "158",
            "priceCurrency": "CNY",
            "availability": "https://schema.org/InStock",
            "priceValidUntil": "2030-12-31",
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "applicableCountry": "CN",
                "returnPolicySeasonalOverride": false,
                "merchantReturnDays": 7,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/FreeReturn"
            },
            "shippingDetails": {
                "@type": "OfferShippingDetails",
                "shippingDestination": {
                    "@type": "DefinedRegion",
                    "addressCountry": "CN"
                },
                "shippingRate": {
                    "@type": "MonetaryAmount",
                    "value": "0",
                    "currency": "CNY"
                },
                "deliveryTime": {
                    "@type": "ShippingDeliveryTime",
                    "handlingTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    },
                    "transitTime": {
                        "@type": "QuantitativeValue",
                        "minValue": 0,
                        "maxValue": 0,
                        "unitCode": "DAY"
                    }
                }
            }
        }
    ]
}
</script>
<link rel="stylesheet" href="./CSS/bootstrap.min.css?t=2023091301">
<style>
html,table{font-size:15px}
*{-webkit-user-select:text!important;-moz-user-select:text!important;-ms-user-select:text!important;user-select:text!important}
th{border:1px solid #dee2e6;text-align:center;font-weight:bold}
td{border:1px solid #dee2e6}
.v-1{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);color:white;background-color:#6666FF}
.v0{color:white;background-color:rgb(23,198,83)}
.v1{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);color:#F9D9A8;background-color:#4B4B4B}
.v3{background:linear-gradient(to right,#FFEBC1 21.65%,#FFE5B7 79.13%);color:#944800;border-top-right-radius:0.05rem;background-color:#944800}
.column-highlight{position:absolute;border-radius:5px;box-shadow:0 0 10px 8px rgba(0,0,255,0.5);-ms-box-shadow:0 0 10px 8px rgba(0,0,255,0.5);pointer-events:none;opacity:0;transition:opacity 0.2s ease,left 0.2s ease;background-color:transparent;z-index:10}
</style>
</head>
<body>
<header>
<h1 style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">VIP会员权益详情</h1>
</header>
<main>
<div class="card-body">
<table class="table table-striped" id="tbMain" aria-label="VIP会员特权对比表">
<caption style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">会员版本功能比较</caption>
<thead>
<tr>
<th style="min-width: 90px" scope="col">权益</th>
<%
var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();
lstUserTypes.Insert(0, UserTypeHelper.GetUserType(UserTypeEnum.体验版));
foreach (var item in lstUserTypes)
{%>
<th class="v<%=item.Type.GetHashCode()%>" scope="col"><%=item.Type.ToString().Replace("体验","免费") %>
</th>
<%
} %>
</tr>
</thead>
            <tbody>
                <tr>
                    <td>区别</td>
                    <%if (lstUserTypes.Any(p => p.Type == UserTypeEnum.体验版))
                        {  %>
                    <td>基础功能，永久免费</td>
                    <%} %>
                    <td>个人专属，轻松识别</td>
                    <td>专业高效，精确稳定</td>
                    <td>旗舰体验，最佳选择</td>
                </tr>
                <tr id="toUpdate" style="display: none;">
                    <td></td>
                    <%
                        for (int i = 0; i < lstUserTypes.Count; i++)
                        {
                            var item = lstUserTypes[i];
                            var url = item.Type.GetHashCode() == 0 ? Account.Web.CommonRequest.GetDownLoadUrl(Request) : "Upgrade.aspx?type=" + item.Type.GetHashCode();
                    %>
                    <td>
                        <a class="v<%=item.Type.GetHashCode()%>" style="display: inline-block; width: 100%; height: 40px; border-radius: 20px; font-size: 18px; font-weight: 500; text-align: center; line-height: 40px; cursor: pointer;"
                            href="<%=url %>"><%=item.Type.GetHashCode() == 0?"免费下载":"升级到"+item.Type.ToString() %></a>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>截图</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>✔</td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>贴图</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>✔</td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>截图识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>✔</td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>离线识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportLocalOcr)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>图片识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportImageFile)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>区域识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportVertical)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>竖排识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportVertical)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>划词翻译</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportTranslate)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>图片翻译</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportTranslate)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>公式识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportMath)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>表格识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportTable)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>文档识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportDocFile)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>文档翻译</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportDocFile)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>批量识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportBatch)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>自选通道</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportPassage)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>多结果</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSetOtherResult)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>多设备</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><b><%=item.MaxLoginCount>1?item.MaxLoginCount.ToString():"✘" %></b></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>识别频率</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><b><%=(item.PerTimeSpan>0? (item.PerTimeSpan / 1000).ToString("F0")+"秒/"+item.PerTimeSpanExecCount+"次":"-").Replace("/1次","/次") %></b></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td><b>每日限额</b></td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><b><%=item.LimitPerDayCount+"次"%></b></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>专属客服</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.MaxLoginCount > 1)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>需求定制</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportTranslate)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        }
                    %>
                </tr>
            </tbody>
        </table>
    </div>
    </main>
<script>
(window.addEventListener?window.addEventListener('DOMContentLoaded',init,false):window.attachEvent?window.attachEvent('onload',init):window.onload=init);
function init(){var u=location.search.substr(1).split('&').reduce(function(o,p){var r=p.split('=');if(r[0]){try{o[r[0]]=decodeURIComponent(r[1]||'');}catch(e){o[r[0]]=r[1]||'';}}return o;},{});var t=document.getElementById("toUpdate");if(t)t.style.display=self!=top&&!u.frame?"":"none";if(!u.shownormal||parseInt(u.shownormal)===0){hideColumn(1);}var c=2;var y=u.type?parseInt(u.type):-2;if(y>-2){var h=document.getElementById('tbMain').getElementsByTagName('th');for(var i=0;i<h.length;i++){if(h[i].className.indexOf('v'+y)!==-1&&h[i].style.display!='none'){c=i;break;}}}highlightCol(c);var l=document.getElementById('tbMain').getElementsByTagName("td");var d=document.getElementById('tbMain').getElementsByTagName("th");addHoverEvents(l);addHoverEvents(d);}
function hideColumn(c){var t=document.getElementById('tbMain');var h=t.rows[0].cells[c];if(h)h.style.display='none';for(var i=0;i<t.rows.length;i++){var l=t.rows[i].cells[c];if(l)l.style.display='none';}}
function addHoverEvents(c){for(var i=0;i<c.length;i++){if(window.addEventListener){c[i].addEventListener('mouseenter',hoverHandler,false);}else if(c[i].attachEvent){c[i].attachEvent('onmouseenter',function(){var x=window.event.srcElement.cellIndex;if(x>0)highlightCol(x);});}}}
function hoverHandler(){var x=this.cellIndex;if(x>0)highlightCol(x);}
function highlightCol(x){var h=document.getElementById('col-highlight')||createHighlightDiv();var t=document.getElementById('tbMain');var c=t.rows[0].cells[x];var r=t.getBoundingClientRect();var e=c.getBoundingClientRect();h.style.left=(e.left-r.left+5)+'px';h.style.top='5px';h.style.width=(c.offsetWidth-10)+'px';h.style.height=(t.offsetHeight-10)+'px';var b=c.currentStyle?c.currentStyle.backgroundColor:getComputedStyle(c,null).backgroundColor;h.style.boxShadow='0 0 10px 8px '+b;h.style.opacity='1';}
function createHighlightDiv(){var d=document.createElement('div');d.id='col-highlight';d.className='column-highlight';document.body.appendChild(d);return d;}
</script>
    <div id="translate" class="ignore" style="display: none;"></div>
    <script src="/static/js/translate.js"></script>
</body>
</html>
