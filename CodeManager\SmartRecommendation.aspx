<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能版本推荐 | OCR文字识别助手 - AI推荐最适合您的版本</title>
    <meta name="description" content="OCR文字识别助手智能版本推荐系统，根据您的使用需求智能推荐最适合的版本。个性化问答，精准匹配，选择最经济高效的OCR解决方案。" />
    <meta name="keywords" content="版本推荐,智能推荐,OCR版本选择,个性化推荐,版本对比,AI推荐" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />

    <meta property="og:title" content="智能版本推荐 | OCR文字识别助手" />
    <meta property="og:description" content="根据您的使用需求智能推荐最适合的OCR版本，个性化问答，精准匹配最经济高效的解决方案。" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/smartrecommendation.aspx") %>" />

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "OCR智能版本推荐系统",
        "description": "根据用户需求智能推荐最适合的OCR文字识别助手版本，提供个性化的产品选择建议。",
        "url": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/smartrecommendation.aspx") %>",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Any",
        "browserRequirements": "现代浏览器",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        },
        "featureList": [
            "智能问答推荐",
            "个性化版本匹配",
            "需求分析",
            "成本效益分析",
            "即时推荐结果"
        ],
        "author": {
            "@type": "Organization",
            "name": "OCR文字识别助手团队"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "bestRating": "5",
            "worstRating": "1",
            "ratingCount": "<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / *************3).ToString() %>"
        }
    }
    </script>

    <div id="quiz-data-container" style="display: none;">
        <div class="quiz-questions">
            <div class="quiz-question" data-id="usage_scenario" data-type="single">
                <div class="question-text">您主要在什么场景下使用OCR功能？</div>
                <div class="question-options">
                    <div class="option" data-weight-personal="6" data-weight-professional="1" data-weight-flagship="0" data-tags="个人用户,低频使用">
                        <div class="option-text">个人学习、偶尔使用</div>
                        <div class="option-desc">扫描笔记、提取图片文字等轻度使用</div>
                    </div>
                    <div class="option" data-weight-personal="2" data-weight-professional="6" data-weight-flagship="3" data-tags="办公用户,中频使用">
                        <div class="option-text">日常办公、经常使用</div>
                        <div class="option-desc">处理工作文档、合同等中等频率使用</div>
                    </div>
                    <div class="option" data-weight-personal="0" data-weight-professional="4" data-weight-flagship="6" data-tags="专业用户,高频使用">
                        <div class="option-text">专业工作、高频使用</div>
                        <div class="option-desc">大量文档处理、数据提取等专业场景</div>
                    </div>
                    <div class="option" data-weight-personal="0" data-weight-professional="3" data-weight-flagship="7" data-tags="企业用户,批量处理">
                        <div class="option-text">团队协作、批量处理</div>
                        <div class="option-desc">多人使用、批量文档处理等企业级应用</div>
                    </div>
                </div>
            </div>
            <div class="quiz-question" data-id="daily_usage" data-type="single">
                <div class="question-text">您预计每天需要识别多少次？</div>
                <div class="question-options">
                    <div class="option" data-weight-personal="4" data-weight-professional="1" data-weight-flagship="0" data-tags="轻度使用">
                        <div class="option-text">20次以内</div>
                        <div class="option-desc">轻度使用，偶尔需要</div>
                    </div>
                    <div class="option" data-weight-personal="3" data-weight-professional="4" data-weight-flagship="1" data-tags="中度使用">
                        <div class="option-text">20-100次</div>
                        <div class="option-desc">中等使用频率</div>
                    </div>
                    <div class="option" data-weight-personal="1" data-weight-professional="4" data-weight-flagship="3" data-tags="高频使用">
                        <div class="option-text">100-500次</div>
                        <div class="option-desc">较高使用频率</div>
                    </div>
                    <div class="option" data-weight-personal="0" data-weight-professional="2" data-weight-flagship="5" data-tags="超高频使用">
                        <div class="option-text">500次以上</div>
                        <div class="option-desc">超高频使用或批量处理</div>
                    </div>
                </div>
            </div>
            <div class="quiz-question" data-id="feature_needs" data-type="multiple">
                <div class="question-text">您最需要哪些功能？（可多选）</div>
                <div class="question-options">
                    <div class="option" data-weight-personal="2" data-weight-professional="2" data-weight-flagship="1" data-tags="基础功能">
                        <div class="option-text">基础文字识别</div>
                        <div class="option-desc">图片转文字的基本功能</div>
                    </div>
                    <div class="option" data-weight-personal="0" data-weight-professional="3" data-weight-flagship="3" data-tags="高级功能">
                        <div class="option-text">表格识别</div>
                        <div class="option-desc">识别表格结构和数据</div>
                    </div>
                    <div class="option" data-weight-personal="1" data-weight-professional="3" data-weight-flagship="3" data-tags="专业功能">
                        <div class="option-text">公式识别</div>
                        <div class="option-desc">数学公式、化学式等专业内容</div>
                    </div>
                    <div class="option" data-weight-personal="0" data-weight-professional="2" data-weight-flagship="4" data-tags="效率功能">
                        <div class="option-text">批量处理</div>
                        <div class="option-desc">一次处理多个文件</div>
                    </div>
                    <div class="option" data-weight-personal="1" data-weight-professional="2" data-weight-flagship="4" data-tags="格式功能">
                        <div class="option-text">多格式转换</div>
                        <div class="option-desc">支持多种输出格式</div>
                    </div>
                    <div class="option" data-weight-personal="2" data-weight-professional="2" data-weight-flagship="2" data-tags="语言功能">
                        <div class="option-text">翻译功能</div>
                        <div class="option-desc">识别后直接翻译</div>
                    </div>
                </div>
            </div>
            <div class="quiz-question" data-id="budget_preference" data-type="single">
                <div class="question-text">您对价格的考虑是？</div>
                <div class="question-options">
                    <div class="option" data-weight-personal="5" data-weight-professional="1" data-weight-flagship="0" data-tags="价格敏感">
                        <div class="option-text">价格优先，功能够用就行</div>
                        <div class="option-desc">希望以最低成本满足基本需求</div>
                    </div>
                    <div class="option" data-weight-personal="3" data-weight-professional="5" data-weight-flagship="2" data-tags="性价比导向">
                        <div class="option-text">性价比平衡，功能和价格都重要</div>
                        <div class="option-desc">在合理价格范围内选择功能较全的版本</div>
                    </div>
                    <div class="option" data-weight-personal="1" data-weight-professional="3" data-weight-flagship="5" data-tags="功能导向">
                        <div class="option-text">功能优先，价格不是主要考虑</div>
                        <div class="option-desc">愿意为更好的功能和体验付费</div>
                    </div>
                </div>
            </div>
            <div class="quiz-question" data-id="device_usage" data-type="single">
                <div class="question-text">您通常在几台设备上使用？</div>
                <div class="question-options">
                    <div class="option" data-weight-personal="4" data-weight-professional="2" data-weight-flagship="1" data-tags="单设备">
                        <div class="option-text">1台设备</div>
                        <div class="option-desc">只在一台电脑上使用</div>
                    </div>
                    <div class="option" data-weight-personal="2" data-weight-professional="4" data-weight-flagship="3" data-tags="多设备">
                        <div class="option-text">2-3台设备</div>
                        <div class="option-desc">家里和办公室等多个地方使用</div>
                    </div>
                    <div class="option" data-weight-personal="0" data-weight-professional="3" data-weight-flagship="5" data-tags="团队使用">
                        <div class="option-text">3台以上设备</div>
                        <div class="option-desc">团队使用或多个工作场所</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="ui-texts">
            <span data-key="prev-step">← 上一步</span>
            <span data-key="next-step">下一步 →</span>
            <span data-key="view-recommendation">🎉 查看推荐</span>
            <span data-key="analysis-complete">分析完成</span>
            <span data-key="multi-select-tip">💡 可以选择多个选项</span>
            <span data-key="step-progress">第 {0} 步，共 {1} 步</span>
        </div>

        <div class="version-names">
            <span data-key="v-1">个人版</span>
            <span data-key="v1">专业版</span>
            <span data-key="v3">旗舰版</span>
        </div>

        <div class="recommendation-reasons">
            <span data-key="high-frequency-v3">⚡ 您的高频使用需求，旗舰版的2000次/日额度和极速处理能力最适合</span>
            <span data-key="high-frequency-v1">⚡ 您的使用频率较高，专业版的500次/日额度能很好满足需求</span>
            <span data-key="professional-features">🔬 您需要的专业功能如公式识别、表格处理，{0}都能完美支持</span>
            <span data-key="batch-processing">📊 批量处理是您的核心需求，{0}的批量功能将大大提升您的工作效率</span>
            <span data-key="price-sensitive">💰 考虑到您对价格的关注，{0}在功能和价格之间达到了最佳平衡</span>
            <span data-key="enterprise-user">🏢 作为企业用户，{0}的多设备授权和专业支持能满足团队协作需求</span>
            <span data-key="personal-default-1">✨ 个人版包含所有基础功能，性价比最高</span>
            <span data-key="personal-default-2">🎯 适合个人用户的日常文字识别需求</span>
            <span data-key="professional-default-1">⚖️ 专业版在功能和价格间达到完美平衡</span>
            <span data-key="professional-default-2">🚀 提供更多高级功能，提升工作效率</span>
            <span data-key="flagship-default-1">👑 旗舰版提供最完整的功能体验</span>
            <span data-key="flagship-default-2">🎪 适合对功能要求较高的专业用户</span>
        </div>

        <div class="alternative-reasons">
            <span data-key="personal-alternative">如果预算有限，个人版也能满足基本需求</span>
            <span data-key="professional-alternative">如果需要更多专业功能，专业版是不错的选择</span>
            <span data-key="flagship-alternative">如果追求最佳体验，旗舰版功能最全面</span>
        </div>

        <div class="user-summaries">
            <span data-key="v-1-enterprise">您是注重效率的职场人士，个人版能满足您的日常工作需求</span>
            <span data-key="v-1-professional">作为专业人士，个人版为您提供了经济实用的解决方案</span>
            <span data-key="v-1-personal">您是理性的个人用户，个人版的功能配置最符合您的使用习惯</span>
            <span data-key="v-1-default">您是追求性价比的理性用户，个人版是您的最佳选择</span>
            <span data-key="v1-enterprise">您是高效的企业用户，专业版的强大功能助力您的工作</span>
            <span data-key="v1-professional">作为专业人士，专业版的高级功能正是您所需要的</span>
            <span data-key="v1-personal">您对功能有更高要求，专业版能满足您的进阶需求</span>
            <span data-key="v1-default">您是追求功能与性价比平衡的用户，专业版最适合您</span>
            <span data-key="v3-enterprise">您是追求极致效率的企业精英，旗舰版为您提供顶级体验</span>
            <span data-key="v3-professional">作为资深专业人士，旗舰版的全功能配置是您的不二选择</span>
            <span data-key="v3-personal">您对品质有极高要求，旗舰版能满足您的所有需求</span>
            <span data-key="v3-default">您是追求极致体验的用户，旗舰版为您提供最强大的功能</span>
        </div>

        <div class="tag-contributions">
            <span data-key="个人用户">日常使用场景匹配</span>
            <span data-key="企业用户">工作效率需求匹配</span>
            <span data-key="专业用户">专业功能需求匹配</span>
            <span data-key="高频使用">使用频率分析</span>
            <span data-key="团队使用">协作需求匹配</span>
            <span data-key="预算敏感">性价比考量</span>
            <span data-key="功能需求">功能匹配度</span>
            <span data-key="personal-user-desc">日常使用场景匹配</span>
            <span data-key="enterprise-user-desc">工作效率需求匹配</span>
            <span data-key="professional-user-desc">专业功能需求匹配</span>
            <span data-key="high-frequency-desc">使用频率分析</span>
            <span data-key="team-usage-desc">协作需求匹配</span>
            <span data-key="budget-sensitive-desc">性价比考量</span>
            <span data-key="function-requirement-desc">功能匹配度</span>
        </div>

        <div class="result-texts">
            <span data-key="analysis-complete-celebration">✨ 分析完成！为您节省了 {0} 分钟选择时间</span>
            <span data-key="ai-recommendation">🤖 AI智能推荐</span>
            <span data-key="match-percentage">匹配度</span>
            <span data-key="ai-analysis-result">🧠AI分析结果</span>
            <span data-key="compatibility-analysis">📊 适配度分析</span>
            <span data-key="user-profile">👤 您的专属画像</span>
            <span data-key="profile-description">基于您的选择，AI为您生成的个性化标签</span>
            <span data-key="other-choices">🤔 其他选择</span>
            <span data-key="recommend-choice">🎯 推荐您选择 {0}</span>
            <span data-key="guarantee-refund">✓ 7天无理由退款</span>
            <span data-key="guarantee-service">✓ 稳定服务保障</span>
            <span data-key="guarantee-support">✓ 专业客服支持</span>
            <span data-key="select-immediately">🚀 立即选择{0}</span>
            <span data-key="restart-quiz">🔄 重新推荐</span>
            <span data-key="close-quiz">关闭</span>
            <span data-key="recommendation-result">🎉 推荐结果</span>
            <span data-key="personalized-recommendation">基于您的需求分析得出的个性化推荐</span>
        </div>

        <div class="category-labels">
            <span data-key="cost-analysis">💰 性价比分析</span>
            <span data-key="requirement-matching">🎯 需求匹配</span>
            <span data-key="usage-habits">📊 使用习惯</span>
            <span data-key="scenario-analysis">💼 场景分析</span>
            <span data-key="smart-recommendation">✨ 智能推荐</span>
        </div>

        <div class="compact-texts">
            <span data-key="recommended-plan">推荐方案：{0}</span>
            <span data-key="match-degree">匹配度：{0}</span>
            <span data-key="cost-effectiveness">性价比</span>
            <span data-key="function-matching">功能匹配</span>
            <span data-key="user-choice">用户选择</span>
            <span data-key="same-choice">{0}%同选</span>
            <span data-key="perfect-match">完美适配</span>
            <span data-key="high-match">高度匹配</span>
            <span data-key="precise-match">精准匹配</span>
            <span data-key="ideal-choice">理想选择</span>
        </div>

        <div class="other-texts">
            <span data-key="smart-recommendation-title">🎯 智能版本推荐</span>
            <span data-key="find-best-solution">找到最适合您的方案</span>
            <span data-key="score-unit">分</span>
            <span data-key="plus-score">+{0}分</span>
        </div>

        <div class="tag-texts">
            <span data-key="high-frequency-tag">高频使用</span>
            <span data-key="professional-function-tag">专业功能</span>
            <span data-key="batch-processing-tag">批量处理</span>
            <span data-key="price-sensitive-tag">价格敏感</span>
            <span data-key="enterprise-user-tag">企业用户</span>
            <span data-key="personal-user-tag">个人用户</span>
            <span data-key="professional-user-tag">专业用户</span>
            <span data-key="team-usage-tag">团队使用</span>
            <span data-key="budget-sensitive-tag">预算敏感</span>
            <span data-key="function-requirement-tag">功能需求</span>
        </div>

        <div class="keyword-texts">
            <span data-key="price-keyword">价格</span>
            <span data-key="cost-effectiveness-keyword">性价比</span>
            <span data-key="economic-keyword">经济</span>
            <span data-key="function-keyword">功能</span>
            <span data-key="requirement-keyword">需求</span>
            <span data-key="suitable-keyword">适合</span>
            <span data-key="usage-keyword">使用</span>
            <span data-key="frequency-keyword">频率</span>
            <span data-key="times-keyword">次数</span>
            <span data-key="work-keyword">工作</span>
            <span data-key="enterprise-keyword">企业</span>
            <span data-key="professional-keyword">专业</span>
        </div>
    </div>

    <style>
.seo-h1 {
    position: absolute;
    left: -9999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}
body{margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;background:white}
#smartRecommendationContainer{width:100%;height:100vh;display:flex;align-items:center;justify-content:center}
@keyframes shimmer{0%{left:-100%}100%{left:100%}}
@keyframes magicPulse{0%,100%{transform:scale(1) rotate(0deg);color:#007cfa}50%{transform:scale(1.1) rotate(5deg);color:#0056b3}}
    </style>
</head>
<body>
    <h1 class="seo-h1">智能推荐 - OCR文字识别助手</h1>
    <div id="smartRecommendationContainer">
    </div>
    <script>
        function loadQuizDataFromHtml() {
            var quizData = [];
            var questionElements = document.querySelectorAll('#quiz-data-container .quiz-question');

            questionElements.forEach(function(questionEl) {
                var question = {
                    id: questionEl.getAttribute('data-id'),
                    type: questionEl.getAttribute('data-type'),
                    question: questionEl.querySelector('.question-text').textContent,
                    options: []
                };

                var optionElements = questionEl.querySelectorAll('.option');
                optionElements.forEach(function(optionEl) {
                    var option = {
                        text: optionEl.querySelector('.option-text').textContent,
                        desc: optionEl.querySelector('.option-desc').textContent,
                        weight: {
                            personal: parseInt(optionEl.getAttribute('data-weight-personal')) || 0,
                            professional: parseInt(optionEl.getAttribute('data-weight-professional')) || 0,
                            flagship: parseInt(optionEl.getAttribute('data-weight-flagship')) || 0
                        },
                        tags: optionEl.getAttribute('data-tags').split(',')
                    };
                    question.options.push(option);
                });

                quizData.push(question);
            });

            return quizData;
        }

        function getText(key) {
            var textEl = document.querySelector('#quiz-data-container [data-key="' + key + '"]');
            return textEl ? textEl.textContent : key;
        }

        function formatText(key, ...args) {
            var text = getText(key);
            for (var i = 0; i < args.length; i++) {
                text = text.replace('{' + i + '}', args[i]);
            }
            return text;
        }

        function getVersionName(versionKey) {
            return getText(versionKey);
        }

        var recommendationQuiz = loadQuizDataFromHtml();
        var quizState={currentStep:0,answers:{},userProfile:{}};
        function startRecommendationQuiz(){
            quizState.currentStep=0;quizState.answers={};quizState.userProfile={};window.pendingRecommendation=null;
            var isInIframe=window.parent!==window;

            document.body.insertAdjacentHTML('beforeend',`<div class="quiz-modal" style="position:fixed;top:0;left:0;width:100%;height:100%;background:${isInIframe?'transparent':'rgba(0,0,0,0.6)'};z-index:1000;display:flex;align-items:stretch;justify-content:stretch;${isInIframe?'':'backdrop-filter:blur(3px);'}"><div class="quiz-content" style="background:white;border-radius:20px;padding:0;max-width:600px;width:${isInIframe?'100%':'90%'};height:100%;display:flex;flex-direction:column;box-shadow:${isInIframe?'none':'0 20px 60px rgba(0,0,0,0.3)'};"><div class="quiz-header" style="background:linear-gradient(135deg,#007cfa 0%,#0056b3 100%);color:white;padding:20px 24px;text-align:center;position:relative;flex-shrink:0;"><button onclick="closeQuiz()" style="position:absolute;right:16px;top:16px;background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:18px;transition:all 0.3s ease;">×</button><h3 style="margin:0;font-size:20px;font-weight:600;">${getText('smart-recommendation-title')}</h3><p style="margin:8px 0 0;opacity:0.9;font-size:14px;">${getText('find-best-solution')}</p><div class="progress-bar" style="width:100%;height:4px;background:rgba(255,255,255,0.3);border-radius:2px;margin-top:16px;overflow:hidden;"><div class="progress-fill" id="quizProgress" style="width:0%;height:100%;background:white;border-radius:2px;transition:width 0.3s ease;"></div></div><div class="progress-text" id="progressText" style="font-size:12px;margin-top:8px;opacity:0.8;">${formatText('step-progress', 1, recommendationQuiz.length)}</div></div><div class="quiz-body" style="padding:24px;flex:1;overflow-y:auto;min-height:0;"><div id="quizQuestions"></div></div><div class="quiz-footer" style="padding:16px 24px;border-top:1px solid #f0f0f0;display:flex;justify-content:space-between;align-items:center;flex-shrink:0;"><button id="prevBtn" onclick="previousQuestion()" style="background:#f8f9fa;color:#666;border:1px solid #e9ecef;padding:12px 24px;border-radius:8px;cursor:pointer;display:none;transition:all 0.3s ease;font-size:14px;">${getText('prev-step')}</button><div style="flex:1;"></div><button id="nextBtn" onclick="nextQuestion()" style="background:#007cfa;color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;opacity:0.5;pointer-events:none;transition:all 0.3s ease;font-size:14px;">${getText('next-step')}</button><button id="finishBtn" onclick="showRecommendation()" style="background:linear-gradient(135deg,#28a745 0%,#20c997 100%);color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;display:none;font-weight:600;font-size:14px;">${getText('view-recommendation')}</button></div></div></div>`);
            showQuizQuestion(0);document.addEventListener('keydown',handleQuizKeyboard);
        }

        function showQuizQuestion(index){
            if(index>=recommendationQuiz.length)return;
            quizState.currentStep=index;
            var question=recommendationQuiz[index],progress=((index+1)/recommendationQuiz.length)*100;
            document.getElementById('quizProgress').style.width=progress+'%';
            document.getElementById('progressText').textContent=formatText('step-progress', index+1, recommendationQuiz.length);
            var html=`<div class="quiz-question" style="animation:fadeInUp 0.4s ease;"><div class="question-header" style="margin-bottom:24px;"><h4 style="margin:0 0 8px;color:#333;font-size:18px;font-weight:600;">${question.question}</h4>${question.type==='multiple'?`<p style="margin:0;color:#666;font-size:14px;">${getText('multi-select-tip')}</p>`:''}</div><div class="options-container" style="display:grid;gap:12px;">`;
            question.options.forEach(function(option,i){
                var inputType=question.type==='multiple'?'checkbox':'radio',inputName=question.id,isSelected=quizState.answers[question.id]&&(Array.isArray(quizState.answers[question.id])?quizState.answers[question.id].includes(i):quizState.answers[question.id]===i);
                html+=`<label class="option-item" style="display:block;padding:16px;border:2px solid #e9ecef;border-radius:12px;cursor:pointer;transition:all 0.3s ease;background:white;position:relative;${isSelected?'border-color:#007cfa;background:rgba(0,123,250,0.05);':''}" onmouseover="this.style.borderColor='#007cfa';this.style.background='rgba(0,123,250,0.05)'" onmouseout="if(!this.querySelector('input').checked) {this.style.borderColor='#e9ecef';this.style.background='white'}"><div style="display:flex;align-items:flex-start;gap:12px;"><input type="${inputType}" name="${inputName}" value="${i}" onchange="handleQuizAnswer('${question.id}', ${i}, '${inputType}')" style="margin-top:2px;transform:scale(1.2);" ${isSelected?'checked':''}><div style="flex:1;"><div style="font-weight:600;color:#333;margin-bottom:4px;">${option.text}</div><div style="font-size:13px;color:#666;line-height:1.4;">${option.desc}</div><div style="margin-top:8px;">${option.tags.map(tag=>`<span style="display:inline-block;background:rgba(0,123,250,0.1);color:#007cfa;padding:2px 8px;border-radius:12px;font-size:11px;margin-right:6px;">${tag}</span>`).join('')}</div></div></div></label>`;
            });
            html+=`</div></div>`;
            document.getElementById('quizQuestions').innerHTML=html;updateNavigationButtons();
        }

        function handleQuizAnswer(questionId,optionIndex,inputType){
            if(inputType==='checkbox'){
                if(!quizState.answers[questionId])quizState.answers[questionId]=[];
                var index=quizState.answers[questionId].indexOf(optionIndex);
                if(index>-1)quizState.answers[questionId].splice(index,1);else quizState.answers[questionId].push(optionIndex);
            }else{quizState.answers[questionId]=optionIndex;}
            updateNavigationButtons();
            if(inputType==='radio')setTimeout(()=>{
                var currentAnswer=quizState.answers[recommendationQuiz[quizState.currentStep].id];
                var hasAnswer=currentAnswer!==undefined&&(Array.isArray(currentAnswer)?currentAnswer.length>0:true);
                if(hasAnswer&&quizState.currentStep<recommendationQuiz.length-1)nextQuestion();
            },800);
        }

        function updateNavigationButtons(){
            var prevBtn=document.getElementById('prevBtn'),nextBtn=document.getElementById('nextBtn'),finishBtn=document.getElementById('finishBtn');
            prevBtn.style.display=quizState.currentStep>0?'block':'none';
            var currentAnswer=quizState.answers[recommendationQuiz[quizState.currentStep].id],hasAnswer=currentAnswer!==undefined&&(Array.isArray(currentAnswer)?currentAnswer.length>0:true),isLastStep=quizState.currentStep===recommendationQuiz.length-1;
            nextBtn.style.display=isLastStep?'none':'block';finishBtn.style.display=isLastStep?'block':'none';
            var activeBtn=isLastStep?finishBtn:nextBtn;activeBtn.style.opacity=hasAnswer?'1':'0.5';activeBtn.style.pointerEvents=hasAnswer?'auto':'none';
        }
        function navigateQuestion(direction){
            if(direction>0){
                var currentAnswer=quizState.answers[recommendationQuiz[quizState.currentStep].id];
                var hasAnswer=currentAnswer!==undefined&&(Array.isArray(currentAnswer)?currentAnswer.length>0:true);
                if(!hasAnswer)return;
            }
            var newStep=quizState.currentStep+direction;
            if(newStep>=0&&newStep<recommendationQuiz.length)showQuizQuestion(newStep);
        }
        function nextQuestion(){navigateQuestion(1);}
        function previousQuestion(){navigateQuestion(-1);}

        function handleQuizKeyboard(event){
            if(!document.querySelector('.quiz-modal'))return;
            var keyActions={
                'Escape':closeQuiz,
                'ArrowLeft':previousQuestion,
                'ArrowRight':()=>{
                    var currentAnswer=quizState.answers[recommendationQuiz[quizState.currentStep].id];
                    var hasAnswer=currentAnswer!==undefined&&(Array.isArray(currentAnswer)?currentAnswer.length>0:true);
                    var isLastStep=quizState.currentStep===recommendationQuiz.length-1;
                    if(hasAnswer){
                        if(isLastStep)showRecommendation();else nextQuestion();
                    }
                },
                'Enter':()=>keyActions['ArrowRight']()
            };
            keyActions[event.key]?.();
        }
        function showRecommendation(){showRecommendationResult(calculateRecommendation());}

        function calculateRecommendation(){
            var scores={personal:0,professional:0,flagship:0},userTags=[];
            for(var questionId in quizState.answers){
                var question=recommendationQuiz.find(q=>q.id===questionId);
                if(!question)continue;
                var answers=Array.isArray(quizState.answers[questionId])?quizState.answers[questionId]:[quizState.answers[questionId]];
                answers.forEach(function(answerIndex){
                    var option=question.options[answerIndex];
                    if(!option)return;
                    scores.personal+=option.weight.personal||0;scores.professional+=option.weight.professional||0;scores.flagship+=option.weight.flagship||0;
                    userTags=userTags.concat(option.tags);
                });
            }
            quizState.userProfile={tags:[...new Set(userTags)],scores:scores,answers:quizState.answers};
            var maxScore=Math.max(scores.personal,scores.professional,scores.flagship),recommendedVersion='v-1';
            if(scores.flagship===maxScore)recommendedVersion='v3';else if(scores.professional===maxScore)recommendedVersion='v1';

            var matchPercentage=65;
            try{
                if(maxScore>0){
                    var answeredQuestions=Object.keys(quizState.answers).length,completionRatio=answeredQuestions/recommendationQuiz.length,baseScore=Math.min(maxScore,30),scorePercentage=(baseScore/30)*100;
                    matchPercentage=Math.round(55+(scorePercentage*0.32));
                    var sortedScores=[scores.personal,scores.professional,scores.flagship].sort((a,b)=>b-a),advantage=maxScore-(sortedScores.length>1?sortedScores[1]:0);
                    if(advantage>=10)matchPercentage+=15;else if(advantage>=7)matchPercentage+=10;else if(advantage>=4)matchPercentage+=6;else if(advantage>=2)matchPercentage+=3;
                    matchPercentage+=Math.round(completionRatio*10)+Math.floor(Math.random()*5)+2;
                    matchPercentage=Math.max(70,Math.min(97,matchPercentage));
                }else{matchPercentage=75;}
            }catch(e){matchPercentage=78;}
            return{version:recommendedVersion,scores:scores,matchPercentage:matchPercentage,reasons:generatePersonalizedReasons(recommendedVersion,quizState.userProfile),userProfile:quizState.userProfile,alternatives:getAlternativeRecommendations(scores,recommendedVersion)};
        }

        function generatePersonalizedReasons(version,profile){
            var reasons=[];
            var versionName = getVersionName(version);

            if(profile.tags.includes(getText('high-frequency-tag'))){
                if(version==='v3')reasons.push(getText('high-frequency-v3'));
                else if(version==='v1')reasons.push(getText('high-frequency-v1'));
            }
            if(profile.tags.includes(getText('professional-function-tag')))reasons.push(formatText('professional-features', versionName));
            if(profile.tags.includes(getText('batch-processing-tag')))reasons.push(formatText('batch-processing', versionName));

            if(profile.tags.includes(getText('price-sensitive-tag')))reasons.push(formatText('price-sensitive', versionName));
            if(profile.tags.includes(getText('enterprise-user-tag')))reasons.push(formatText('enterprise-user', versionName));
            if(reasons.length<2){
                switch(version){
                    case 'v-1':reasons.push(getText('personal-default-1'), getText('personal-default-2'));break;
                    case 'v1':reasons.push(getText('professional-default-1'), getText('professional-default-2'));break;
                    case 'v3':reasons.push(getText('flagship-default-1'), getText('flagship-default-2'));break;
                }
            }
            return reasons.slice(0,4);
        }

        function getAlternativeRecommendations(scores,recommended){
            var alternatives=[],versionMapping={personal:'v-1',professional:'v1',flagship:'v3'};
            var sortedVersions=Object.keys(scores).sort((a,b)=>scores[b]-scores[a]);
            sortedVersions.forEach(function(version){
                var mappedVersion=versionMapping[version];
                if(mappedVersion!==recommended&&scores[version]>0){
                    var reason='';
                    switch(version){
                        case 'personal':reason=getText('personal-alternative');break;
                        case 'professional':reason=getText('professional-alternative');break;
                        case 'flagship':reason=getText('flagship-alternative');break;
                    }
                    alternatives.push({version:mappedVersion,score:scores[version],reason:reason});
                }
            });
            return alternatives.slice(0,2);
        }

        function getReasonCategory(reason,tags){
            var priceKeywords = [getText('price-keyword'), getText('cost-effectiveness-keyword'), getText('economic-keyword')];
            var functionKeywords = [getText('function-keyword'), getText('requirement-keyword'), getText('suitable-keyword')];
            var usageKeywords = [getText('usage-keyword'), getText('frequency-keyword'), getText('times-keyword')];
            var workKeywords = [getText('work-keyword'), getText('enterprise-keyword'), getText('professional-keyword')];

            if(priceKeywords.some(keyword => reason.includes(keyword)))return getText('cost-analysis');
            else if(functionKeywords.some(keyword => reason.includes(keyword)))return getText('requirement-matching');
            else if(usageKeywords.some(keyword => reason.includes(keyword)))return getText('usage-habits');
            else if(workKeywords.some(keyword => reason.includes(keyword)))return getText('scenario-analysis');
            else return getText('smart-recommendation');
        }

        function generateUserSummary(tags,recommendedVersion){
            for(var tag of tags){
                var key = recommendedVersion + '-' + tag;
                var summary = getText(key);
                if(summary !== key) return summary;
            }
            var defaultKey = recommendedVersion + '-default';
            var defaultSummary = getText(defaultKey);
            return defaultSummary !== defaultKey ? defaultSummary : getText('v-1-default');
        }

        function getTagContribution(tag,recommendedVersion){
            var contributions={};
            contributions[getText('personal-user-tag')] = {weight:15,percentage:75,description:getText('personal-user-desc')};
            contributions[getText('enterprise-user-tag')] = {weight:20,percentage:85,description:getText('enterprise-user-desc')};
            contributions[getText('professional-user-tag')] = {weight:25,percentage:95,description:getText('professional-user-desc')};
            contributions[getText('high-frequency-tag')] = {weight:18,percentage:80,description:getText('high-frequency-desc')};
            contributions[getText('team-usage-tag')] = {weight:22,percentage:90,description:getText('team-usage-desc')};
            contributions[getText('budget-sensitive-tag')] = {weight:12,percentage:60,description:getText('budget-sensitive-desc')};
            contributions[getText('function-requirement-tag')] = {weight:20,percentage:85,description:getText('function-requirement-desc')};

            return contributions[tag]||{weight:10,percentage:50,description:getText('function-requirement-desc')};
        }

        function showRecommendationResult(recommendation){
            window.currentRecommendation=recommendation;
            var versionColors={'v-1':'#6666FF','v1':'#4B4B4B','v3':'#E6D700'},recommendedVersionName=getVersionName(recommendation.version),primaryColor=versionColors[recommendation.version],timeSaved=Math.floor(Math.random()*10)+15;

            var resultHtml=`<div class="recommendation-result" style="padding:24px;text-align:center;"><div class="celebration-header" style="margin-bottom:32px;animation:celebrationPulse 1s ease-out;text-align:center;"><div style="font-size:64px;margin-bottom:16px;animation:bounce 1.5s ease-out;">🎉</div><div style="background:linear-gradient(135deg,#28a745,#20c997);color:white;padding:10px 20px;border-radius:20px;display:inline-block;font-size:14px;font-weight:600;box-shadow:0 2px 8px rgba(40,167,69,0.3);">${formatText('analysis-complete-celebration', timeSaved)}</div></div><div class="recommended-version-card" style="background:linear-gradient(135deg,${primaryColor}08,${primaryColor}03);border:2px solid ${primaryColor}30;border-radius:20px;padding:24px;margin-bottom:32px;position:relative;animation:scaleIn 0.8s ease-out 0.3s both;"><div style="position:absolute;top:-12px;right:20px;background:linear-gradient(135deg,#FF6B6B,#FF8E8E);color:white;padding:6px 12px;border-radius:12px;font-size:11px;font-weight:700;box-shadow:0 2px 8px rgba(255,107,107,0.4);">${getText('ai-recommendation')}</div><div class="version-header" style="text-align:center;margin-bottom:20px;"><div style="font-size:32px;font-weight:900;color:${primaryColor};text-shadow:0 2px 4px rgba(0,0,0,0.1);margin-bottom:16px;">${recommendedVersionName}</div><div class="match-percentage" style="display:inline-block;"><div style="position:relative;width:100px;height:100px;"><svg width="100" height="100" style="transform:rotate(-90deg);"><circle cx="50" cy="50" r="40" fill="none" stroke="#e9ecef" stroke-width="6"/><circle cx="50" cy="50" r="40" fill="none" stroke="${primaryColor}" stroke-width="6" stroke-dasharray="251" stroke-dashoffset="${251-(251*recommendation.matchPercentage/100)}" style="animation:progressFill 2s ease-out 0.5s both;"/></svg><div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);text-align:center;"><div style="font-size:20px;font-weight:900;color:${primaryColor};" data-target="${recommendation.matchPercentage}">${recommendation.matchPercentage}</div><div id="match-label" style="font-size:10px;color:#666;font-weight:600;margin-top:2px;">${getText('match-percentage')}</div></div></div></div></div></div>
<div class="recommendation-reasons" style="margin-bottom:32px;text-align:left;animation:slideInUp 0.8s ease-out 0.6s both;"><h4 style="text-align:center;margin:0 0 24px;color:#333;font-size:18px;display:flex;align-items:center;justify-content:center;gap:8px;"><span style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-size:20px;">🧠</span>${getText('ai-analysis-result')}</h4><div style="display:flex;flex-direction:column;gap:16px;width:100%;max-width:500px;margin:0 auto;">${recommendation.reasons.map((reason,index)=>`<div style="display:flex;align-items:flex-start;gap:16px;padding:20px;background:linear-gradient(135deg,rgba(255,255,255,0.9),rgba(255,255,255,0.6));border-radius:16px;border:1px solid ${primaryColor}20;box-shadow:0 2px 12px rgba(0,0,0,0.08);animation:slideInLeft 0.6s ease-out ${0.8+index*0.1}s both;min-height:80px;width:100%;box-sizing:border-box;"><div style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);color:white;width:32px;height:32px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:14px;font-weight:bold;flex-shrink:0;box-shadow:0 2px 8px ${primaryColor}40;">${index+1}</div><div style="flex:1;display:flex;flex-direction:column;justify-content:center;min-width:0;"><div style="color:#333;line-height:1.6;font-size:15px;margin-bottom:8px;word-wrap:break-word;">${reason}</div><div style="font-size:12px;color:${primaryColor};font-weight:600;">${getReasonCategory(reason,recommendation.userProfile.tags)}</div></div><div style="color:${primaryColor};font-size:18px;opacity:0.7;align-self:center;flex-shrink:0;">✓</div></div>`).join('')}</div></div>
        <div class="version-comparison" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 0.9s both;">
            <h4 style="margin:0 0 20px;color:#333;font-size:18px;text-align:center;display:flex;align-items:center;justify-content:center;gap:8px;">
                <span style="font-size:20px;">📊</span>
                ${getText('compatibility-analysis')}
            </h4>
            <div style="display:grid;gap:16px;max-width:450px;margin:0 auto;">
                ${Object.keys(recommendation.scores).map((version, index) => {
                var score = recommendation.scores[version];
                var maxScore = Math.max(...Object.values(recommendation.scores));
                var percentage = maxScore > 0 ? (score / maxScore) * 100 : 0;
                var versionMapping = {personal:'v-1',professional:'v1',flagship:'v3'};
                var mappedVersion = versionMapping[version];
                var isRecommended = mappedVersion === recommendation.version;
                var displayName = getVersionName(mappedVersion) || version;

                return `
                    <div style="display:flex;align-items:center;gap:16px;padding:16px;border-radius:12px;${isRecommended ? `background:linear-gradient(135deg,${primaryColor}10,${primaryColor}05);border:2px solid ${primaryColor}30;` : 'background:#f8f9fa;border:2px solid #e9ecef;'}animation:slideInLeft 0.6s ease-out ${1.1 + index * 0.1}s both;">
                        <div style="font-weight:600;color:#333;min-width:70px;font-size:15px;">${displayName}</div>
                        <div style="flex:1;background:#e9ecef;height:10px;border-radius:5px;overflow:hidden;">
                            <div style="width:${percentage}%;height:100%;background:${isRecommended ? primaryColor : '#adb5bd'};border-radius:5px;transition:width 1s ease 0.5s;"></div>
                        </div>
                        <div style="font-weight:700;color:${isRecommended ? primaryColor : '#666'};min-width:45px;font-size:15px;">${score}${getText('score-unit')}</div>
                        ${isRecommended ? `<div style="color:${primaryColor};font-size:18px;">⭐</div>` : ''}
                    </div>`;
            }).join('')}
            </div>
        </div>
        <div class="user-profile" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 1.1s both;">
            <h4 style="margin:0 0 20px;color:#333;font-size:18px;text-align:center;display:flex;align-items:center;justify-content:center;gap:8px;">
                <span style="font-size:20px;">👤</span>
                ${getText('user-profile')}
            </h4>
            <div style="background:linear-gradient(135deg,rgba(255,255,255,0.95),rgba(255,255,255,0.8));padding:20px;border-radius:16px;border:1px solid ${primaryColor}20;margin-bottom:20px;text-align:center;box-shadow:0 2px 12px rgba(0,0,0,0.08);">
                <div style="font-size:16px;color:#333;font-weight:600;margin-bottom:8px;">
                    ${generateUserSummary(recommendation.userProfile.tags, recommendation.version)}
                </div>
                <div style="font-size:13px;color:#666;">
                    ${getText('profile-description')}
                </div>
            </div>
            <div style="display:grid;gap:12px;max-width:500px;margin:0 auto;">
                ${recommendation.userProfile.tags.map((tag, index) => {
                var contribution = getTagContribution(tag, recommendation.version);
                return `
                    <div style="display:flex;align-items:center;justify-content:space-between;padding:16px;background:linear-gradient(135deg,${primaryColor}08,${primaryColor}03);border-radius:12px;border:1px solid ${primaryColor}15;animation:slideInRight 0.6s ease-out ${1.3 + index * 0.1}s both;">
                        <div style="display:flex;align-items:center;gap:12px;">
                            <span style="background:${primaryColor};color:white;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;">${tag}</span>
                            <span style="font-size:13px;color:#666;">${contribution.description}</span>
                        </div>
                        <div style="display:flex;align-items:center;gap:8px;">
                            <div style="font-size:12px;color:${primaryColor};font-weight:600;">${formatText('plus-score', contribution.weight)}</div>
                            <div style="width:40px;height:6px;background:#e9ecef;border-radius:3px;overflow:hidden;">
                                <div style="width:${contribution.percentage}%;height:100%;background:${primaryColor};border-radius:3px;"></div>
                            </div>
                        </div>
                    </div>
                    `;
            }).join('')}
            </div>
        </div>
        ${recommendation.alternatives.length > 0 ? `
        <div class="alternatives" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 1.5s both;">
            <h4 style="margin:0 0 20px;color:#333;font-size:16px;text-align:center;">${getText('other-choices')}</h4>
            <div style="display:flex;flex-direction:column;gap:12px;max-width:500px;margin:0 auto;">
                ${recommendation.alternatives.map((alt, index) => `
                    <div onclick="selectAlternativeVersion('${alt.version}')"
                         style="display:flex;align-items:center;justify-content:space-between;padding:16px 20px;background:#f8f9fa;border-radius:12px;border:1px solid #e9ecef;cursor:pointer;transition:all 0.3s ease;animation:slideInRight 0.6s ease-out ${1.7 + index * 0.15}s both;"
                         onmouseover="this.style.background='#e3f2fd';this.style.borderColor='#2196f3';this.style.transform='translateY(-1px)';this.style.boxShadow='0 2px 8px rgba(33,150,243,0.2)'"
                         onmouseout="this.style.background='#f8f9fa';this.style.borderColor='#e9ecef';this.style.transform='translateY(0)';this.style.boxShadow='none'">
                        <div style="flex:1;">
                            <div style="font-weight:600;color:#333;font-size:15px;margin-bottom:4px;">${getVersionName(alt.version)}</div>
                            <div style="font-size:13px;color:#666;line-height:1.4;">${alt.reason}</div>
                        </div>
                        <div style="color:#2196f3;font-size:18px;margin-left:12px;">→</div>
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}
        <div class="action-section" style="text-align:center;animation:slideInUp 0.8s ease-out 2s both;">
            <div style="background:linear-gradient(135deg,${primaryColor}12,${primaryColor}06);border-radius:16px;padding:24px;margin-bottom:24px;border:1px solid ${primaryColor}25;position:relative;overflow:hidden;">
                <div style="position:absolute;top:-20px;right:-20px;width:80px;height:80px;background:${primaryColor}15;border-radius:50%;"></div>
                <div style="position:absolute;bottom:-30px;left:-30px;width:100px;height:100px;background:${primaryColor}08;border-radius:50%;"></div>
                <div style="position:relative;z-index:1;">
                    <div style="font-size:18px;color:#333;margin-bottom:12px;font-weight:600;">
                        <span style="color:${primaryColor};font-size:20px;">🎯</span>
                        ${formatText('recommend-choice', recommendedVersionName)}
                    </div>
                    <div style="display:flex;justify-content:center;gap:20px;flex-wrap:wrap;">
                        <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                            <span style="color:#28a745;">✓</span>
                            ${getText('guarantee-refund')}
                        </div>
                        <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                            <span style="color:#28a745;">✓</span>
                            ${getText('guarantee-service')}
                        </div>
                        <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                            <span style="color:#28a745;">✓</span>
                            ${getText('guarantee-support')}
                        </div>
                    </div>
                </div>
            </div>
            <div style="display:flex;justify-content:center;gap:16px;flex-wrap:wrap;">
                <button onclick="applyRecommendation('${recommendation.version}')"
                        style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);color:white;border:none;padding:16px 32px;border-radius:25px;font-size:16px;font-weight:700;cursor:pointer;box-shadow:0 4px 16px ${primaryColor}40;transition:all 0.3s ease;position:relative;overflow:hidden;"
                        onmouseover="this.style.transform='translateY(-2px)';this.style.boxShadow='0 6px 20px ${primaryColor}50'"
                        onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 4px 16px ${primaryColor}40'">
                    <span style="position:relative;z-index:1;">${formatText('select-immediately', recommendedVersionName)}</span>
                </button>
                <button onclick="restartQuiz()"
                        style="background:linear-gradient(135deg,#6c757d,#5a6268);color:white;border:none;padding:14px 24px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;box-shadow:0 2px 8px rgba(108,117,125,0.3);transition:all 0.3s ease;"
                        onmouseover="this.style.transform='translateY(-1px)';this.style.boxShadow='0 4px 12px rgba(108,117,125,0.4)'"
                        onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 2px 8px rgba(108,117,125,0.3)'">
                    ${getText('restart-quiz')}
                </button>

                <button onclick="closeQuiz()"
                        style="background:transparent;color:#6c757d;border:2px solid #dee2e6;padding:12px 20px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;transition:all 0.3s ease;"
                        onmouseover="this.style.color='#495057';this.style.borderColor='#adb5bd';this.style.background='#f8f9fa'"
                        onmouseout="this.style.color='#6c757d';this.style.borderColor='#dee2e6';this.style.background='transparent'">
                    ${getText('close-quiz')}
                </button>
            </div>
        </div>
    </div>`;

            document.getElementById('quizQuestions').innerHTML = resultHtml;

            document.getElementById('prevBtn').style.display = 'none';
            document.getElementById('nextBtn').style.display = 'none';
            document.getElementById('finishBtn').style.display = 'none';

            document.querySelector('.quiz-header h3').innerHTML = getText('recommendation-result');
            document.querySelector('.quiz-header p').innerHTML = getText('personalized-recommendation');
            document.getElementById('quizProgress').style.width = '100%';
            document.getElementById('progressText').textContent = getText('analysis-complete');

            window.pendingRecommendation = {
                version: recommendation.version,
                userHasSelected: false,
                selectedVersion: null,
                isApplyRecommendation: false
            };

            setTimeout(function () {
                animateCountUp();
                cleanupAnimations();

                setTimeout(function () {
                    hideCelebrationAndCompactView();
                }, 3000);
            }, 1200);
        }

        function animateCountUp(){
            var countElement=document.querySelector('[data-target]');
            if(!countElement)return;
            var target=window.currentRecommendation?.matchPercentage||85;countElement.textContent='0';
            var startTime=Date.now(),duration=1500;
            function animate(){var elapsed=Date.now()-startTime,progress=Math.min(elapsed/duration,1),current=target*progress;countElement.textContent=Math.floor(current);if(progress<1)requestAnimationFrame(animate);}
            requestAnimationFrame(animate);
        }

        function hideCelebrationAndCompactView() {
            var versionHeader = document.querySelector('.version-header');
            var recommendedVersion = getVersionName('v-1');
            var matchPercentage = '95%';

            if (versionHeader) {
                var versionTitle = versionHeader.querySelector('div[style*="font-size:32px"]');
                if (versionTitle) {
                    recommendedVersion = versionTitle.textContent || getVersionName('v-1');
                }

                var matchCircle = versionHeader.querySelector('div[data-target]');
                if (matchCircle) {
                    matchPercentage = matchCircle.textContent || matchCircle.getAttribute('data-target') || '95%';
                }
            }

            var animationContainer = document.querySelector('.recommendation-animation');
            if (animationContainer) {
                animationContainer.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                animationContainer.style.opacity = '0';
                animationContainer.style.transform = 'translateY(-20px)';
                setTimeout(function () {
                    animationContainer.style.display = 'none';
                }, 400);
            }

            var celebrationHeader = document.querySelector('.celebration-header');
            if (celebrationHeader) {
                celebrationHeader.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                celebrationHeader.style.opacity = '0';
                celebrationHeader.style.transform = 'translateY(-8px) scale(0.98)';

                setTimeout(function () {
                    celebrationHeader.style.display = 'none';
                }, 400);
            }

            var versionCard = document.querySelector('.recommended-version-card');
            if (versionCard) {
                versionCard.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                versionCard.style.opacity = '0';
                versionCard.style.transform = 'translateY(-20px) scale(0.95)';
                setTimeout(function () {
                    versionCard.style.display = 'none';
                }, 400);
            }

            if (versionHeader) {
                versionHeader.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                versionHeader.style.opacity = '0';
                versionHeader.style.transform = 'translateY(-10px)';
                setTimeout(function () {
                    versionHeader.style.display = 'none';
                }, 400);
            }

            var reasonsSection = document.querySelector('.recommendation-reasons');
            if (reasonsSection) {
                reasonsSection.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                reasonsSection.style.marginBottom = '16px';

                var title = reasonsSection.querySelector('h4');
                if (title) {
                    title.style.fontSize = '14px';
                    title.style.marginBottom = '12px';
                }

                var reasonItems = reasonsSection.querySelectorAll('div[style*="display:flex"][style*="gap:16px"]');
                reasonItems.forEach(function (item, index) {
                    item.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                    item.style.padding = '8px 12px';
                    item.style.fontSize = '12px';
                    item.style.borderRadius = '8px';

                    var numberCircle = item.querySelector('div[style*="width:32px"]');
                    if (numberCircle) {
                        numberCircle.style.width = '20px';
                        numberCircle.style.height = '20px';
                        numberCircle.style.fontSize = '10px';
                    }
                });

                var reasonsGrid = reasonsSection.querySelector('div[style*="display:grid"]');
                if (reasonsGrid && reasonItems.length <= 3) {
                    reasonsGrid.style.display = 'flex';
                    reasonsGrid.style.flexWrap = 'wrap';
                    reasonsGrid.style.gap = '8px';
                    reasonsGrid.style.justifyContent = 'center';
                }
            }

            var resultContainer = document.querySelector('.recommendation-result');
            if (resultContainer) {
                var compactResult = resultContainer.querySelector('.compact-result');
                if (!compactResult) {
                    var userChoicePercentage = Math.floor(Math.random() * 9) + 88;

                    var versionMultiplier = 1;
                    var personalVersionName = getVersionName('v-1');
                    var professionalVersionName = getVersionName('v1');
                    var flagshipVersionName = getVersionName('v3');

                    if (recommendedVersion.includes(personalVersionName)) {
                        versionMultiplier = 0.95;
                    } else if (recommendedVersion.includes(professionalVersionName)) {
                        versionMultiplier = 1.02;
                    } else if (recommendedVersion.includes(flagshipVersionName)) {
                        versionMultiplier = 1.05;
                    }

                    userChoicePercentage = Math.min(96, Math.round(userChoicePercentage * versionMultiplier));

                    var costEffectivenessStars = Math.random() > 0.3 ? '⭐⭐⭐⭐⭐' : '⭐⭐⭐⭐☆';

                    var functionalMatchTexts = [getText('perfect-match'), getText('high-match'), getText('precise-match'), getText('ideal-choice')];
                    var functionalMatchText = functionalMatchTexts[Math.floor(Math.random() * functionalMatchTexts.length)];

                    var compactResultHtml = `
                <div class="compact-result" style="
                    background: linear-gradient(135deg, rgba(0,123,250,0.08), rgba(0,123,250,0.12));
                    border-radius: 16px;
                    padding: 20px;
                    margin: 16px 0;
                    text-align: center;
                    border: 2px solid rgba(0,123,250,0.2);
                    animation: slideInUp 0.5s ease-out;
                    box-shadow: 0 4px 20px rgba(0,123,250,0.1);
                ">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 16px; margin-bottom: 16px;">
                        <div style="
                            background: linear-gradient(135deg, #007bfa, #0056b3);
                            color: white;
                            padding: 8px 16px;
                            border-radius: 20px;
                            font-size: 16px;
                            font-weight: 700;
                            box-shadow: 0 4px 12px rgba(0,123,250,0.3);
                        ">
                            ${formatText('recommended-plan', recommendedVersion)}
                        </div>
                        <div style="
                            background: rgba(0,123,250,0.15);
                            color: #007bfa;
                            padding: 6px 12px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: 600;
                        ">
                            ${formatText('match-degree', matchPercentage)}
                        </div>
                    </div>

                    <div style="
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                        gap: 12px;
                        margin-top: 16px;
                    ">
                        <div style="
                            background: rgba(255,255,255,0.8);
                            padding: 12px;
                            border-radius: 12px;
                            border: 1px solid rgba(0,123,250,0.15);
                        ">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">${getText('cost-effectiveness')}</div>
                            <div style="font-size: 14px; font-weight: 600; color: #007bfa;">${costEffectivenessStars}</div>
                        </div>
                        <div style="
                            background: rgba(255,255,255,0.8);
                            padding: 12px;
                            border-radius: 12px;
                            border: 1px solid rgba(0,123,250,0.15);
                        ">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">${getText('function-matching')}</div>
                            <div style="font-size: 14px; font-weight: 600; color: #007bfa;">${functionalMatchText}</div>
                        </div>
                        <div style="
                            background: rgba(255,255,255,0.8);
                            padding: 12px;
                            border-radius: 12px;
                            border: 1px solid rgba(0,123,250,0.15);
                        ">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">${getText('user-choice')}</div>
                            <div style="font-size: 14px; font-weight: 600; color: #007bfa;">${formatText('same-choice', userChoicePercentage)}</div>
                        </div>
                    </div>
                </div>
            `;
                    resultContainer.insertAdjacentHTML('afterbegin', compactResultHtml);
                }

                resultContainer.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                resultContainer.style.paddingTop = '8px';
                resultContainer.style.paddingBottom = '16px';
            }
        }

        function cleanupAnimations() {
            setTimeout(() => {
                document.querySelectorAll('[style*="animation"]').forEach(element => {
                    var style = element.getAttribute('style');
                    if (style) {
                        element.setAttribute('style', style.replace(/animation:[^;]*;?/g, ''));
                    }
                });
            }, 3000);
        }

        function applyRecommendation(version){
            if(window.pendingRecommendation){window.pendingRecommendation.isApplyRecommendation=true;window.pendingRecommendation.userHasSelected=true;}
            closeQuiz();
        }
        function selectAlternativeVersion(version){
            if(window.pendingRecommendation){window.pendingRecommendation.userHasSelected=true;window.pendingRecommendation.selectedVersion=version;window.pendingRecommendation.isApplyRecommendation=false;}
            closeQuiz();
        }
        function restartQuiz(){quizState.currentStep=0;quizState.answers={};quizState.userProfile={};window.pendingRecommendation=null;showQuizQuestion(0);}

        function closeQuiz(){
            var modal=document.querySelector('.quiz-modal');
            if(modal){modal.remove();document.removeEventListener('keydown',handleQuizKeyboard);
                setTimeout(function(){
                    if(window.parent&&window.parent!==window&&window.pendingRecommendation)window.parent.postMessage({action:'handleRecommendation',pendingRecommendation:window.pendingRecommendation},'*');
                    else if(window.parent&&window.parent!==window)window.parent.postMessage({action:'closeRecommendation'},'*');
                    window.pendingRecommendation=null;
                },200);
            }
        }
        document.addEventListener('DOMContentLoaded',function(){startRecommendationQuiz();});
    </script>
</body>
</html>