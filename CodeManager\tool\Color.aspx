<%@ Page Title="在线取色器 | 调色板 | 网页设计配色工具" Language="C#" MasterPageFile="~/tool/Tool.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" href="./static/css/bootstrap.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="keywords" content="颜色选择器,调色板,颜色,配色工具,RGB,CMYK,HLS,hex,HTML颜色代码,在线调色板,取色工具,网页调色板,调色板工具,设计师,定制颜色,色值转换">
    <meta name="description" content="免费在线取色器和调色板工具，为设计师提供颜色选择、比对和配色功能，支持RGB、HEX、HSL和CMYK等多种颜色模式互转，帮助您创建完美的网页设计配色方案。">

    <meta property="og:title" content="在线取色器 | 调色板 | 网页设计配色工具" />
    <meta property="og:description" content="免费在线取色器和调色板工具，支持RGB、HEX、HSL和CMYK等多种颜色模式互转。" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/tool/color.aspx") %>" />

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "在线取色器调色板工具",
        "description": "免费在线取色器和调色板工具，为设计师提供颜色选择、比对和配色功能。",
        "url": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/tool/color.aspx") %>",
        "applicationCategory": "DesignApplication",
        "operatingSystem": "Any",
        "browserRequirements": "现代浏览器",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        },
        "featureList": [
            "颜色选择器",
            "调色板工具",
            "RGB、HEX、HSL、CMYK颜色模式转换",
            "网页设计配色",
            "颜色比对功能",
            "免费使用"
        ],
        "author": {
            "@type": "Organization",
            "name": "OCR文字识别助手团队"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.4",
            "bestRating": "5",
            "worstRating": "1",
            "ratingCount": "<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / *************2).ToString() %>"
        }
    }
    </script>
</asp:Content>

<asp:Content ID="BreadcrumbContent" ContentPlaceHolderID="BreadcrumbSchema" runat="server">
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "首页",
                "item": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/default.aspx") %>"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "在线工具",
                "item": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/tool.aspx") %>"
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": "在线取色器",
                "item": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/tool/color.aspx") %>"
            }
        ]
    }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="runoob-page-content">
        <h1 style="text-align: center; padding: 20px 0; margin: 0; font-size: 1.8rem; color: #333;">在线颜色工具 | RGB颜色代码转换器</h1>
        <style type="text/css">
            .cp {
                margin: 20px;
            }

            #color {
                width: 200px;
                height: 200px;
                float: left;
                margin: 20px;
                border: 1px solid black;
            }

            #text-color {
                float: left;
                margin-top: 30px;
            }
            /* Common stuff */
            .picker-wrapper,
            .slide-wrapper {
                position: relative;
                float: left;
            }

            .picker-indicator,
            .slide-indicator {
                position: absolute;
                left: 0;
                top: 0;
                pointer-events: none;
            }

            .picker,
            .slide {
                cursor: crosshair;
                float: left;
            }

            /* Default skin */

            .cp-default {
                background-color: gray;
                padding: 12px;
                box-shadow: 0 0 40px #000;
                border-radius: 15px;
                float: left;
            }

                .cp-default .picker {
                    width: 200px;
                    height: 200px;
                }

                .cp-default .slide {
                    width: 30px;
                    height: 200px;
                }

                .cp-default .slide-wrapper {
                    margin-left: 10px;
                }

                .cp-default .picker-indicator {
                    width: 5px;
                    height: 5px;
                    border: 2px solid darkblue;
                    -moz-border-radius: 4px;
                    -o-border-radius: 4px;
                    -webkit-border-radius: 4px;
                    border-radius: 4px;
                    opacity: .5;
                    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
                    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50);
                    filter: alpha(opacity=50);
                    background-color: white;
                }

                .cp-default .slide-indicator {
                    width: 100%;
                    height: 10px;
                    /*  left: -4px;*/
                    opacity: .6;
                    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=60)";
                    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=60);
                    filter: alpha(opacity=60);
                    border: 4px solid lightblue;
                    -moz-border-radius: 4px;
                    -o-border-radius: 4px;
                    -webkit-border-radius: 4px;
                    border-radius: 4px;
                    background-color: white;
                }

            /* Normal skin */

            .cp-normal {
                padding: 10px;
                background-color: white;
                float: left;
                border: 4px solid #d6d6d6;
                box-shadow: inset 0 0 10px white;
            }

                .cp-normal .picker {
                    width: 200px;
                    height: 200px;
                }

                .cp-normal .slide {
                    width: 30px;
                    height: 200px;
                }

                .cp-normal .slide-wrapper {
                    margin-left: 10px;
                }

                .cp-normal .picker-indicator {
                    width: 5px;
                    height: 5px;
                    border: 1px solid gray;
                    opacity: .5;
                    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
                    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50);
                    filter: alpha(opacity=50);
                    background-color: white;
                    pointer-events: none;
                }

                .cp-normal .slide-indicator {
                    width: 100%;
                    height: 10px;
                    left: -4px;
                    opacity: .6;
                    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=60)";
                    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=60);
                    filter: alpha(opacity=60);
                    border: 4px solid gray;
                    background-color: white;
                    pointer-events: none;
                }

            .form-control {
                font-weight: bold;
            }
        </style>
        <link rel="stylesheet" href="./static/css/app-color.css" media="all" />
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <form class="form-inline" role="form">
                            <label><strong style="font-size: 16px"><i class="fa fa-cogs"></i>在线取色器工具</strong></label>
                        </form>
                    </div>
                    <div class="card-body">
                        <form role="form">
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div id="default" class="cp cp-default"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <div id="color"></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <table class="table table-condensed">
                                        <tr>
                                            <td colspan="3">
                                                <div class="input-group">
                                                    <div class="input-group-prepend"><span class="input-group-text">HEX</span></div>
                                                    <input id="hex" type="text" class="form-control" value="">
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="3">
                                                <div class="input-group">
                                                    <div class="input-group-prepend"><span class="input-group-text">RGB</span></div>
                                                    <input id="rgb_css" type="text" class="form-control" value="">
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="input-group">
                                                    <div class="input-group-prepend"><span class="input-group-text">R</span></div>
                                                    <input id="rgb_r" type="text" class="form-control" value="">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <div class="input-group-prepend"><span class="input-group-text">G</span></div>
                                                    <input id="rgb_g" type="text" class="form-control" value="">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <div class="input-group-prepend"><span class="input-group-text">B</span></div>
                                                    <input id="rgb_b" type="text" class="form-control" value="">
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="3">
                                                <div class="input-group">
                                                    <div class="input-group-prepend"><span class="input-group-text">HSV</span></div>
                                                    <input id="hsv_css" type="text" class="form-control" value="">
                                                </div>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td>
                                                <div class="input-group">
                                                    <div class="input-group-prepend"><span class="input-group-text">H</span></div>
                                                    <input id="hsv_h" type="text" class="form-control" value="">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <div class="input-group-prepend"><span class="input-group-text">S</span></div>
                                                    <input id="hsv_s" type="text" class="form-control" value="">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <div class="input-group-prepend"><span class="input-group-text">V</span></div>
                                                    <input id="hsv_v" type="text" class="form-control" value="">
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </form>
                        <script src="./static/js/index-color.js"></script>
                    </div>
                </div>
            </div>
        </div>
        <script type="text/javascript" src="./static/js/colorpicker.min.js"></script>

        <script>
            var cpDefault = ColorPicker(document.getElementById('default'), updateInputs);

            var iHex = document.getElementById('hex');
            var iR = document.getElementById('rgb_r');
            var iG = document.getElementById('rgb_g');
            var iB = document.getElementById('rgb_b');
            var iH = document.getElementById('hsv_h');
            var iS = document.getElementById('hsv_s');
            var iV = document.getElementById('hsv_v');

            var rgbCSS = document.getElementById('rgb_css');
            var hsvCSS = document.getElementById('hsv_css');

            var color = document.getElementById('color');
            //var textColor = document.getElementById('text-color');

            function updateInputs(hex) {

                var rgb = ColorPicker.hex2rgb(hex);
                var hsv = ColorPicker.hex2hsv(hex);

                iHex.value = hex;
                // $("#mdc-color").val(hex);
                iR.value = rgb.r;
                iG.value = rgb.g;
                iB.value = rgb.b;

                iH.value = hsv.h.toFixed(2);
                iS.value = hsv.s.toFixed(2);
                iV.value = hsv.v.toFixed(2);

                rgbCSS.value = 'rgb(' + rgb.r + ', ' + rgb.g + ', ' + rgb.b + ')';
                hsvCSS.value = 'hsv(' + hsv.h.toFixed(2) + ', ' + hsv.s.toFixed(2) + ', ' + hsv.v.toFixed(2) + ')';

                color.style.backgroundColor = hex;
                location.hash = hex;
                // textColor.style.color = hex;
            }

            function updateColorPickers(hex) {
                location.hash = hex;
                cpDefault.setHex(hex);
            }


            var initialHex = '#f4329c';
            updateColorPickers(initialHex);


            iHex.onchange = function () { updateColorPickers(iHex.value); };

            iR.onchange = function () { updateColorPickers(ColorPicker.rgb2hex({ r: iR.value, g: iG.value, b: iB.value })); }
            iG.onchange = function () { updateColorPickers(ColorPicker.rgb2hex({ r: iR.value, g: iG.value, b: iB.value })); }
            iB.onchange = function () { updateColorPickers(ColorPicker.rgb2hex({ r: iR.value, g: iG.value, b: iB.value })); }

            iH.onchange = function () { updateColorPickers(ColorPicker.hsv2hex({ h: iH.value, s: iS.value, v: iV.value })); }
            iS.onchange = function () { updateColorPickers(ColorPicker.hsv2hex({ h: iH.value, s: iS.value, v: iV.value })); }
            iV.onchange = function () { updateColorPickers(ColorPicker.hsv2hex({ h: iH.value, s: iS.value, v: iV.value })); }
        </script>
    </div>
</asp:Content>
