﻿<%@ Page Title='' Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" EnableViewState="false" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <%Page.Title = (Request.QueryString["type"] ?? "index") == "index" ? "在线OCR文字识别 | 免费图片转文字 | AI识别体验" :
                 (Request.QueryString["type"] ?? "index") == "text_recognize" ? "在线文字识别 | 图片文字提取 | 智能OCR识别" :
                 (Request.QueryString["type"] ?? "index") == "table" ? "在线表格识别 | 图片表格转Excel | OCR表格提取" :
                 (Request.QueryString["type"] ?? "index") == "pdf2word" ? "PDF转Word | 在线PDF转换 | 文档格式转换" :
                 (Request.QueryString["type"] ?? "index") == "pdf2markdown" ? "PDF转Markdown | 在线文档转换 | MD格式输出" :
                 (Request.QueryString["type"] ?? "index") == "pdf2jpg" ? "PDF转图片 | 在线PDF转JPG | 文档转图像" :
                 (Request.QueryString["type"] ?? "index") == "word2pdf" ? "Word转PDF | 在线文档转换 | DOC转PDF工具" :
                 (Request.QueryString["type"] ?? "index") == "word2jpg" ? "Word转图片 | 在线DOC转JPG | 文档转图像" :
                 (Request.QueryString["type"] ?? "index") == "image2pdf" ? "图片转PDF | 多图合并PDF | 在线图片转换" :
                 (Request.QueryString["type"] ?? "index") == "handwritten_ocr" ? "手写文字识别 | 手写体OCR | 笔迹识别工具" : "在线体验 | 截屏识别 | 批量OCR | 免费试用"; %>
<meta name="description" content="<%=(Request.QueryString["type"] ?? "index") == "index" ? "免费在线OCR文字识别，无需下载即可使用。支持图片转文字，98%识别准确率，100+语言支持，毫秒级响应。" :
                                      (Request.QueryString["type"] ?? "index") == "text_recognize" ? "智能文字识别工具，高精度图片文字提取，支持多语言识别，快速准确，办公学习必备OCR工具。" :
                                      (Request.QueryString["type"] ?? "index") == "table" ? "智能在线表格识别工具，精准提取图片中的表格数据，支持复杂表格结构，导出Excel格式，识别准确率高。" :
                                      (Request.QueryString["type"] ?? "index") == "pdf2word" ? "在线PDF转Word工具，支持扫描版PDF转换，保持原有格式，快速转换，支持批量处理。" :
                                      (Request.QueryString["type"] ?? "index") == "pdf2markdown" ? "PDF转Markdown工具，智能解析PDF文档结构，生成标准MD格式，适合技术文档处理。" :
                                      (Request.QueryString["type"] ?? "index") == "pdf2jpg" ? "PDF转图片工具，高质量PDF转JPG，支持批量转换，自定义分辨率，快速导出图像文件。" :
                                      (Request.QueryString["type"] ?? "index") == "word2pdf" ? "Word转PDF工具，在线DOC转PDF，保持原有格式，支持批量转换，兼容各版本Word文档。" :
                                      (Request.QueryString["type"] ?? "index") == "word2jpg" ? "Word转图片工具，DOC转JPG，高清图像输出，支持批量转换，适合文档预览和分享。" :
                                      (Request.QueryString["type"] ?? "index") == "image2pdf" ? "图片转PDF工具，多张图片合并为PDF文档，支持排序和页面设置，便于文档整理和存档。" :
                                      (Request.QueryString["type"] ?? "index") == "handwritten_ocr" ? "手写文字识别专家，支持中英文手写体识别，笔迹潦草也能准确识别，学习办公好帮手，识别率业界领先。" : "免费在线OCR体验，无需下载即可使用。支持图片转文字、表格识别、文档转换，98%识别准确率。" %>" />
<meta name="keywords" content="<%=(Request.QueryString["type"] ?? "index") == "index" ? "在线OCR,图片转文字,文字识别,免费OCR,AI识别,截屏识别,图像识别,文字提取,OCR工具" :
                                   (Request.QueryString["type"] ?? "index") == "text_recognize" ? "文字识别,图片文字提取,智能OCR,文本识别,图像文字识别,OCR识别,文字提取工具" :
                                   (Request.QueryString["type"] ?? "index") == "table" ? "表格识别,图片表格转换,Excel提取,表格OCR,数据提取,表格转换,在线表格识别,智能表格" :
                                   (Request.QueryString["type"] ?? "index") == "pdf2word" ? "PDF转Word,PDF转DOC,在线PDF转换,文档转换,PDF转换器,扫描PDF转Word" :
                                   (Request.QueryString["type"] ?? "index") == "pdf2markdown" ? "PDF转Markdown,PDF转MD,文档转换,Markdown转换,技术文档转换,PDF解析" :
                                   (Request.QueryString["type"] ?? "index") == "pdf2jpg" ? "PDF转图片,PDF转JPG,PDF转PNG,文档转图片,PDF转换器,批量转换" :
                                   (Request.QueryString["type"] ?? "index") == "word2pdf" ? "Word转PDF,DOC转PDF,文档转换,在线转换,Word转换器,批量转换" :
                                   (Request.QueryString["type"] ?? "index") == "word2jpg" ? "Word转图片,DOC转JPG,文档转图片,Word转换器,文档预览,批量转换" :
                                   (Request.QueryString["type"] ?? "index") == "image2pdf" ? "图片转PDF,多图合并PDF,图片合并,PDF制作,图片转换,文档整理" :
                                   (Request.QueryString["type"] ?? "index") == "handwritten_ocr" ? "手写识别,手写体OCR,笔迹识别,手写文字转换,手写体转文字,笔记识别,手写工具" : "在线OCR,截屏识别,批量识别,免费图片转文字,在线文字识别,表格识别,文档转换" %>" />

<meta property="og:title" content="<%=(Request.QueryString["type"] ?? "index") == "index" ? "在线OCR文字识别 | 免费图片转文字工具" :
                                           (Request.QueryString["type"] ?? "index") == "table" ? "在线表格识别 | 图片表格转Excel工具" :
                                           (Request.QueryString["type"] ?? "index") == "formula" ? "在线公式识别 | 数学公式OCR转LaTeX" :
                                           (Request.QueryString["type"] ?? "index") == "pdf" ? "在线PDF文字提取 | PDF转文字工具" :
                                           (Request.QueryString["type"] ?? "index") == "batch" ? "批量OCR识别 | 多文件批量处理工具" :
                                           (Request.QueryString["type"] ?? "index") == "handwriting" ? "手写文字识别 | 手写体OCR转换工具" : "OCR在线体验中心 | 免费AI文字识别" %>" />
<meta property="og:description" content="<%=(Request.QueryString["type"] ?? "index") == "index" ? "免费在线OCR文字识别工具，无需下载即可使用，支持图片转文字，98%识别准确率。" :
                                            (Request.QueryString["type"] ?? "index") == "table" ? "智能表格识别工具，精准提取图片中的表格数据，支持复杂表格结构识别。" :
                                            (Request.QueryString["type"] ?? "index") == "formula" ? "专业数学公式识别工具，支持LaTeX格式输出，识别准确率高达99%。" :
                                            (Request.QueryString["type"] ?? "index") == "pdf" ? "高效PDF文字提取工具，支持扫描版PDF识别，保持原有格式。" :
                                            (Request.QueryString["type"] ?? "index") == "batch" ? "批量OCR处理工具，支持多文件同时识别，提高工作效率。" :
                                            (Request.QueryString["type"] ?? "index") == "handwriting" ? "手写文字识别专家，支持中英文手写体识别，笔迹潦草也能准确识别。" : "免费在线OCR体验中心，无需下载即可体验AI文字识别，支持多种识别模式。" %>" />
<meta property="og:type" content="website" />
<meta property="og:url" content="<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/ocr.aspx") %>" />

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "OCR在线体验中心",
    "description": "免费在线OCR体验中心，无需下载即可体验AI文字识别。支持图片转文字、表格识别、公式识别、PDF解析。",
    "url": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/ocr.aspx") %>",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Any",
    "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "CNY",
        "availability": "https://schema.org/InStock"
    },
    "featureList": [
        "在线图片转文字",
        "在线表格识别",
        "在线公式识别",
        "在线PDF解析",
        "100+语言支持",
        "毫秒级响应",
        "无需下载安装"
    ],
    "author": {
        "@type": "Organization",
        "name": "OCR文字识别助手团队"
    },
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "bestRating": "5",
        "worstRating": "1",
        "ratingCount": "<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / *************3).ToString() %>"
    }
}
</script>
</asp:Content>

<asp:Content ID="BreadcrumbContent" ContentPlaceHolderID="BreadcrumbSchema" runat="server">
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "首页",
                "item": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/default.aspx") %>"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "在线体验",
                "item": "<%=Account.Web.LanguageService.GenerateSmartCanonicalUrl("/ocr.aspx") %>"
            }
        ]
    }
    </script>
<meta name="keywords" content="免费OCR,在线文字识别,表格识别,图片转文字,公式识别,PDF解析,多语言识别,高精度识别" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
<!-- OCR页面独特内容区域 -->
<div class="ocr-header" style="position: absolute; top: -1px; left: 0; width: 100%; height: 1px; overflow: hidden; font-size: 1px; line-height: 1px; opacity: 0.01;">
    <h1><%=(Request.QueryString["type"] ?? "index") == "index" ? "在线OCR文字识别体验" :
           (Request.QueryString["type"] ?? "index") == "text_recognize" ? "在线文字识别工具" :
           (Request.QueryString["type"] ?? "index") == "table" ? "在线表格识别工具" :
           (Request.QueryString["type"] ?? "index") == "pdf2word" ? "PDF转Word转换工具" :
           (Request.QueryString["type"] ?? "index") == "pdf2markdown" ? "PDF转Markdown工具" :
           (Request.QueryString["type"] ?? "index") == "pdf2jpg" ? "PDF转图片工具" :
           (Request.QueryString["type"] ?? "index") == "word2pdf" ? "Word转PDF工具" :
           (Request.QueryString["type"] ?? "index") == "word2jpg" ? "Word转图片工具" :
           (Request.QueryString["type"] ?? "index") == "image2pdf" ? "图片转PDF工具" :
           (Request.QueryString["type"] ?? "index") == "handwritten_ocr" ? "手写文字识别工具" : "OCR文字识别在线体验" %></h1>
    <div class="ocr-description">
        <p><%=(Request.QueryString["type"] ?? "index") == "index" ? "免费在线OCR体验中心，无需下载即可体验AI文字识别。支持图片转文字、表格识别、文档转换，毫秒级响应。" :
             (Request.QueryString["type"] ?? "index") == "text_recognize" ? "智能文字识别工具，高精度图片文字提取，支持多语言识别，快速准确，办公学习必备。" :
             (Request.QueryString["type"] ?? "index") == "table" ? "智能表格识别工具，精准提取图片中的表格数据，支持复杂表格结构识别，导出Excel格式。" :
             (Request.QueryString["type"] ?? "index") == "pdf2word" ? "在线PDF转Word工具，支持扫描版PDF转换，保持原有格式，快速转换，支持批量处理。" :
             (Request.QueryString["type"] ?? "index") == "pdf2markdown" ? "PDF转Markdown工具，智能解析PDF文档结构，生成标准MD格式，适合技术文档处理。" :
             (Request.QueryString["type"] ?? "index") == "pdf2jpg" ? "PDF转图片工具，高质量PDF转JPG，支持批量转换，自定义分辨率，快速导出图像文件。" :
             (Request.QueryString["type"] ?? "index") == "word2pdf" ? "Word转PDF工具，在线DOC转PDF，保持原有格式，支持批量转换，兼容各版本Word文档。" :
             (Request.QueryString["type"] ?? "index") == "word2jpg" ? "Word转图片工具，DOC转JPG，高清图像输出，支持批量转换，适合文档预览和分享。" :
             (Request.QueryString["type"] ?? "index") == "image2pdf" ? "图片转PDF工具，多张图片合并为PDF文档，支持排序和页面设置，便于文档整理和存档。" :
             (Request.QueryString["type"] ?? "index") == "handwritten_ocr" ? "手写文字识别专家，支持中英文手写体识别，笔迹潦草也能准确识别，学习办公好帮手。" : "专业的OCR文字识别服务，提供多种识别模式。" %></p>
    </div>
    <div class="ocr-features">
        <ul class="feature-list">
            <%if ((Request.QueryString["type"] ?? "index") == "index") { %>
                <li>图片转文字</li><li>98%识别准确率</li><li>100+语言支持</li><li>毫秒级响应</li>
            <%} else if ((Request.QueryString["type"] ?? "index") == "text_recognize") { %>
                <li>智能文字识别</li><li>多语言支持</li><li>高精度提取</li><li>快速处理</li>
            <%} else if ((Request.QueryString["type"] ?? "index") == "table") { %>
                <li>表格结构识别</li><li>数据精准提取</li><li>Excel格式导出</li><li>复杂表格支持</li>
            <%} else if ((Request.QueryString["type"] ?? "index") == "pdf2word") { %>
                <li>PDF转Word</li><li>格式保持</li><li>扫描版支持</li><li>批量转换</li>
            <%} else if ((Request.QueryString["type"] ?? "index") == "pdf2markdown") { %>
                <li>PDF转Markdown</li><li>结构解析</li><li>标准MD格式</li><li>技术文档</li>
            <%} else if ((Request.QueryString["type"] ?? "index") == "pdf2jpg") { %>
                <li>PDF转图片</li><li>高质量输出</li><li>自定义分辨率</li><li>批量处理</li>
            <%} else if ((Request.QueryString["type"] ?? "index") == "word2pdf") { %>
                <li>Word转PDF</li><li>格式保持</li><li>版本兼容</li><li>批量转换</li>
            <%} else if ((Request.QueryString["type"] ?? "index") == "word2jpg") { %>
                <li>Word转图片</li><li>高清输出</li><li>文档预览</li><li>批量处理</li>
            <%} else if ((Request.QueryString["type"] ?? "index") == "image2pdf") { %>
                <li>图片转PDF</li><li>多图合并</li><li>排序设置</li><li>文档整理</li>
            <%} else if ((Request.QueryString["type"] ?? "index") == "handwritten_ocr") { %>
                <li>手写体识别</li><li>中英文支持</li><li>潦草字体识别</li><li>学习办公助手</li>
            <%} else { %>
                <li>专业OCR识别服务</li>
            <%} %>
        </ul>
    </div>
    <div class="ocr-advantages" style="margin-top: 15px;">
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
            <strong>为什么选择我们的OCR服务：</strong>
            <span style="margin-left: 10px; color: #666;">
                <%=(Request.QueryString["type"] ?? "index") == "index" ? "无需下载安装，浏览器直接使用，保护隐私安全" :
                   (Request.QueryString["type"] ?? "index") == "table" ? "专业表格识别算法，处理复杂表格结构，数据提取准确" :
                   (Request.QueryString["type"] ?? "index") == "formula" ? "先进AI算法，支持复杂数学公式，LaTeX标准输出" :
                   (Request.QueryString["type"] ?? "index") == "pdf" ? "智能PDF解析，保持原有排版，支持多页批量处理" :
                   (Request.QueryString["type"] ?? "index") == "batch" ? "高并发处理能力，支持大批量文件，提升工作效率" :
                   (Request.QueryString["type"] ?? "index") == "handwriting" ? "深度学习训练，识别各种手写风格，准确率业界领先" : "专业技术团队，持续优化算法，提供最佳识别体验" %>
            </span>
        </div>
    </div>
</div>

<iframe id="frm" style="padding-top: 50px; width: 100%" frameborder="0" border="0" scrolling="no" allowtransparency="yes" allow="clipboard-read;clipboard-write;fullscreen"></iframe>
<script>
function getCurrentLanguage(){var p=window.location.pathname.split('/');if(p.length>1&&p[1]&&p[1].length>0){var l=p[1];if(l.length>=2&&l.indexOf('.')===-1)return l;}return "zh-Hans";}
function getQueryParam(name){var search=window.location.search.substring(1);var params=search.split('&');for(var i=0;i<params.length;i++){var pair=params[i].split('=');if(decodeURIComponent(pair[0])===name){return pair[1]?decodeURIComponent(pair[1]):'';}}}
var t=getQueryParam('type')||"index";
document.getElementById('frm').src="/"+getCurrentLanguage()+"/ocr/"+t+".html";
var f=document.getElementsByTagName('iframe');
function iResize(){for(var i=0,j=f.length;i<j;i++){var b=f[i].contentWindow&&f[i].contentWindow.document&&f[i].contentWindow.document.body?f[i].contentWindow.document.body.scrollHeight:0;var d=f[i].contentWindow&&f[i].contentWindow.document&&f[i].contentWindow.document.documentElement?f[i].contentWindow.document.documentElement.scrollHeight:0;var c=f[i].document&&f[i].document.documentElement?f[i].document.documentElement.scrollHeight:0;var h=window.innerHeight-100;f[i].style.height=Math.max(Math.max(Math.max(b,d),c),h)+'px';}}
window.setInterval("iResize()",200);
</script>
</asp:Content>
